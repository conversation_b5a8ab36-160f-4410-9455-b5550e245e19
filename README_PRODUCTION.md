# 🎮 Discord Clone - Produktions-Setup

Eine sichere, verschlüsselte Discord-Alternative mit Docker-Deployment.

## ✨ Features

### 🔒 Sicherheit
- **End-to-End Verschlüsselung** aller Nachrichten
- **JWT-basierte Authentifizierung**
- **Sichere Passwort-Hashes** (bcrypt)
- **Session-Management** mit Redis
- **SQL-Injection Schutz**

### 💬 Chat-Features
- **Echtzeit-Messaging** mit WebSockets
- **Text- und Voice-Kanäle**
- **Verschlüsselte Nachrichtenspeicherung**
- **Nachrichtenverlauf**
- **Benutzer-Status** (Online/Offline)

### 🎥 Voice & Video
- **WebRTC Voice-Chat**
- **Video-Übertragung**
- **Bildschirmfreigabe**
- **Mehrere Teilnehmer**

### 🏗️ Architektur
- **PostgreSQL** für persistente Daten
- **Redis** für Sessions und Caching
- **Docker** für einfaches Deployment
- **Go** Backend mit hoher Performance
- **Responsive** Web-Frontend

## 🚀 Schnellstart

### Voraussetzungen
- Docker & Docker Compose
- Git
- 2GB RAM minimum
- 10GB Festplattenspeicher

### 1. Repository klonen
```bash
git clone <repository-url>
cd discord-clone
```

### 2. Deployment starten
```bash
chmod +x deploy.sh
./deploy.sh deploy
```

Das Script:
- ✅ Generiert sichere Geheimnisse
- ✅ Startet alle Services
- ✅ Initialisiert die Datenbank
- ✅ Wartet bis alles bereit ist

### 3. Anwendung öffnen
- **Hauptanwendung**: http://localhost:8080
- **Login/Registrierung**: http://localhost:8080/auth.html
- **Datenbank-Admin**: http://localhost:5050

## 🔧 Konfiguration

### Umgebungsvariablen (.env)
```bash
# Datenbank
DB_HOST=postgres
DB_USER=discord
DB_PASSWORD=<generiert>
DB_NAME=discord_clone

# Redis
REDIS_HOST=redis:6379

# Sicherheit
JWT_SECRET=<generiert>
ENCRYPT_KEY=<generiert>

# Anwendung
APP_ENV=production
APP_PORT=8080
```

### Docker Services
- **app**: Go-Anwendung (Port 8080)
- **postgres**: PostgreSQL Datenbank (Port 5432)
- **redis**: Redis Cache (Port 6379)
- **pgadmin**: Datenbank-Admin (Port 5050)

## 📊 Verwaltung

### Logs anzeigen
```bash
# Alle Services
docker-compose logs -f

# Nur App
./deploy.sh logs app

# Nur Datenbank
./deploy.sh logs postgres
```

### Services verwalten
```bash
# Stoppen
./deploy.sh stop

# Neustarten
./deploy.sh restart

# Aktualisieren
./deploy.sh update
```

### Backup erstellen
```bash
# Automatisches Backup
./deploy.sh backup

# Manuelles Backup
docker-compose exec postgres pg_dump -U discord discord_clone > backup.sql
```

### Backup wiederherstellen
```bash
# Service stoppen
docker-compose stop app

# Datenbank wiederherstellen
docker-compose exec -T postgres psql -U discord -d discord_clone < backup.sql

# Service starten
docker-compose start app
```

## 🔐 Sicherheitsfeatures

### Nachrichtenverschlüsselung
```go
// Nachrichten werden mit AES-256-GCM verschlüsselt
func (app *App) encrypt(plaintext string) (string, string, error) {
    // Generiert zufälligen Nonce
    // Verschlüsselt mit AES-256-GCM
    // Speichert nur verschlüsselte Daten
}
```

### Authentifizierung
- **JWT Tokens** mit 24h Gültigkeit
- **Sichere Session-Verwaltung**
- **Passwort-Hashing** mit bcrypt
- **CSRF-Schutz**

### Datenbank-Sicherheit
- **Prepared Statements** gegen SQL-Injection
- **Verschlüsselte Verbindungen**
- **Audit-Logs** für alle Aktionen
- **Automatische Backups**

## 🌐 Produktions-Deployment

### SSL/HTTPS einrichten
```yaml
# docker-compose.prod.yml
services:
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/ssl/certs
```

### Nginx Konfiguration
```nginx
server {
    listen 443 ssl;
    server_name your-domain.com;
    
    ssl_certificate /etc/ssl/certs/cert.pem;
    ssl_certificate_key /etc/ssl/certs/key.pem;
    
    location / {
        proxy_pass http://app:8080;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
}
```

### Monitoring
```yaml
# Prometheus & Grafana hinzufügen
services:
  prometheus:
    image: prom/prometheus
    ports:
      - "9090:9090"
      
  grafana:
    image: grafana/grafana
    ports:
      - "3000:3000"
```

## 🐛 Troubleshooting

### Häufige Probleme

**Port bereits belegt**
```bash
# Prüfen welcher Prozess Port 8080 verwendet
lsof -i :8080
# Oder anderen Port verwenden
sed -i 's/8080:8080/8081:8080/' docker-compose.yml
```

**Datenbank-Verbindung fehlgeschlagen**
```bash
# Datenbank-Status prüfen
docker-compose exec postgres pg_isready -U discord

# Logs prüfen
docker-compose logs postgres
```

**Services starten nicht**
```bash
# Alle Container stoppen und neu starten
docker-compose down --remove-orphans
docker-compose up --build -d
```

### Performance-Optimierung
```yaml
# docker-compose.yml
services:
  postgres:
    environment:
      - POSTGRES_SHARED_BUFFERS=256MB
      - POSTGRES_EFFECTIVE_CACHE_SIZE=1GB
      
  redis:
    command: redis-server --maxmemory 256mb --maxmemory-policy allkeys-lru
```

## 📈 Skalierung

### Horizontale Skalierung
```yaml
services:
  app:
    deploy:
      replicas: 3
    depends_on:
      - postgres
      - redis
      
  nginx:
    image: nginx:alpine
    # Load Balancer Konfiguration
```

### Datenbank-Replikation
```yaml
services:
  postgres-master:
    image: postgres:15
    
  postgres-replica:
    image: postgres:15
    environment:
      - PGUSER=replicator
```

## 🤝 Beitragen

1. Fork das Repository
2. Feature-Branch erstellen
3. Änderungen committen
4. Pull Request erstellen

## 📄 Lizenz

MIT License - siehe LICENSE Datei

## 🆘 Support

- **Issues**: GitHub Issues
- **Dokumentation**: Wiki
- **Community**: Discord Server
