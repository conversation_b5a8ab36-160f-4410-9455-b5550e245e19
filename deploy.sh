#!/bin/bash

# Discord Clone Deployment Script
set -e

echo "🚀 Discord Clone Deployment Script"
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is installed
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker ist nicht installiert. Bitte installieren Sie Docker zuerst."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose ist nicht installiert. Bitte installieren Sie Docker Compose zuerst."
        exit 1
    fi
    
    print_success "Docker und Docker Compose sind installiert"
}

# Generate secure secrets
generate_secrets() {
    print_status "Generiere sichere Geheimnisse..."
    
    # Generate random secrets
    JWT_SECRET=$(openssl rand -base64 32)
    ENCRYPT_KEY=$(openssl rand -base64 32 | cut -c1-32)
    DB_PASSWORD=$(openssl rand -base64 16)
    
    # Create .env file
    cat > .env << EOF
# Database Configuration
DB_HOST=postgres
DB_USER=discord
DB_PASSWORD=${DB_PASSWORD}
DB_NAME=discord_clone

# Redis Configuration
REDIS_HOST=redis:6379

# Security
JWT_SECRET=${JWT_SECRET}
ENCRYPT_KEY=${ENCRYPT_KEY}

# Application
APP_ENV=production
APP_PORT=8080
EOF
    
    print_success "Geheimnisse generiert und in .env gespeichert"
}

# Update docker-compose with generated secrets
update_docker_compose() {
    print_status "Aktualisiere Docker Compose Konfiguration..."
    
    # Read secrets from .env
    source .env
    
    # Update docker-compose.yml with generated secrets
    sed -i.bak "s/secure_password_123/${DB_PASSWORD}/g" docker-compose.yml
    sed -i.bak "s/your-super-secret-jwt-key-change-this-in-production/${JWT_SECRET}/g" docker-compose.yml
    sed -i.bak "s/your-32-byte-encryption-key-change/${ENCRYPT_KEY}/g" docker-compose.yml
    
    print_success "Docker Compose Konfiguration aktualisiert"
}

# Build and start services
deploy_services() {
    print_status "Baue und starte Services..."
    
    # Stop existing services
    docker-compose down --remove-orphans
    
    # Build and start services
    docker-compose up --build -d
    
    print_success "Services gestartet"
}

# Wait for services to be ready
wait_for_services() {
    print_status "Warte auf Services..."
    
    # Wait for database
    print_status "Warte auf PostgreSQL..."
    until docker-compose exec -T postgres pg_isready -U discord -d discord_clone; do
        sleep 2
    done
    
    # Wait for Redis
    print_status "Warte auf Redis..."
    until docker-compose exec -T redis redis-cli ping; do
        sleep 2
    done
    
    # Wait for application
    print_status "Warte auf Anwendung..."
    until curl -f http://localhost:8080/health &> /dev/null; do
        sleep 2
    done
    
    print_success "Alle Services sind bereit"
}

# Show deployment information
show_info() {
    echo ""
    echo "🎉 Deployment erfolgreich!"
    echo "========================="
    echo ""
    echo "📱 Anwendung: http://localhost:8080"
    echo "🔐 Login: http://localhost:8080/auth.html"
    echo "🗄️  pgAdmin: http://localhost:5050"
    echo "   - Email: <EMAIL>"
    echo "   - Passwort: admin123"
    echo ""
    echo "📊 Logs anzeigen:"
    echo "   docker-compose logs -f app"
    echo ""
    echo "🛑 Services stoppen:"
    echo "   docker-compose down"
    echo ""
    echo "🔄 Services neustarten:"
    echo "   docker-compose restart"
    echo ""
    echo "💾 Datenbank-Backup erstellen:"
    echo "   docker-compose exec postgres pg_dump -U discord discord_clone > backup.sql"
    echo ""
}

# Backup function
create_backup() {
    print_status "Erstelle Datenbank-Backup..."
    
    BACKUP_FILE="backup_$(date +%Y%m%d_%H%M%S).sql"
    docker-compose exec -T postgres pg_dump -U discord discord_clone > "$BACKUP_FILE"
    
    print_success "Backup erstellt: $BACKUP_FILE"
}

# Main deployment process
main() {
    case "${1:-deploy}" in
        "deploy")
            check_docker
            generate_secrets
            update_docker_compose
            deploy_services
            wait_for_services
            show_info
            ;;
        "backup")
            create_backup
            ;;
        "logs")
            docker-compose logs -f "${2:-app}"
            ;;
        "stop")
            print_status "Stoppe Services..."
            docker-compose down
            print_success "Services gestoppt"
            ;;
        "restart")
            print_status "Starte Services neu..."
            docker-compose restart
            print_success "Services neugestartet"
            ;;
        "update")
            print_status "Aktualisiere Services..."
            docker-compose down
            docker-compose pull
            docker-compose up --build -d
            wait_for_services
            print_success "Services aktualisiert"
            ;;
        *)
            echo "Verwendung: $0 [deploy|backup|logs|stop|restart|update]"
            echo ""
            echo "Befehle:"
            echo "  deploy  - Vollständiges Deployment (Standard)"
            echo "  backup  - Datenbank-Backup erstellen"
            echo "  logs    - Logs anzeigen"
            echo "  stop    - Services stoppen"
            echo "  restart - Services neustarten"
            echo "  update  - Services aktualisieren"
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
