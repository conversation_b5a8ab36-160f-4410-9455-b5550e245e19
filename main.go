package main

import (
	"encoding/json"
	"log"
	"net/http"
	"sync"

	"github.com/google/uuid"
	"github.com/gorilla/websocket"
)

const maxHistory = 20

var upgrader = websocket.Upgrader{
	ReadBufferSize:  1024,
	WriteBufferSize: 1024,
	CheckOrigin:     func(r *http.Request) bool { return true },
}

// Message types
const (
	MessageTypeNewMessage    = "new_message"
	MessageTypeUserList      = "user_list"
	MessageTypeHistory       = "history"
	MessageTypeJoinVoice     = "join_voice"
	MessageTypeLeaveVoice    = "leave_voice"
	MessageTypeVoiceUsers    = "voice_users"
	MessageTypeWebRTCOffer   = "webrtc_offer"
	MessageTypeWebRTCAnswer  = "webrtc_answer"
	MessageTypeWebRTCICE     = "webrtc_ice"
	MessageTypeScreenShare   = "screen_share"
	MessageTypeVideoToggle   = "video_toggle"
	MessageTypeAudioToggle   = "audio_toggle"
	MessageTypeCreateChannel = "create_channel"
	MessageTypeDeleteChannel = "delete_channel"
	MessageTypeChannelList   = "channel_list"
)

type Message struct {
	Type    string      `json:"type"`
	Content string      `json:"content"`
	Author  string      `json:"author"`
	Data    interface{} `json:"data,omitempty"`
	Target  string      `json:"target,omitempty"`
	From    string      `json:"from,omitempty"`
}

type VoiceState struct {
	UserID       string `json:"user_id"`
	Username     string `json:"username"`
	AudioMuted   bool   `json:"audio_muted"`
	VideoEnabled bool   `json:"video_enabled"`
	ScreenShare  bool   `json:"screen_share"`
}

type Client struct {
	id           string
	conn         *websocket.Conn
	send         chan []byte
	room         *Room
	username     string
	voiceState   *VoiceState
	voiceChannel string // Track which voice channel the user is connected to
}

type Room struct {
	name         string
	roomType     string // "text" or "voice"
	clients      map[*Client]bool
	voiceClients map[*Client]*VoiceState
	broadcast    chan []byte
	register     chan *Client
	unregister   chan *Client
	history      []Message
	mu           sync.Mutex
}

type Channel struct {
	Name     string `json:"name"`
	Type     string `json:"type"`
	Category string `json:"category"`
}

type Guild struct {
	name     string
	rooms    map[string]*Room
	channels []Channel
	mu       sync.Mutex
}

type Hub struct {
	guilds map[string]*Guild
	mu     sync.Mutex
}

var hub = Hub{
	guilds: make(map[string]*Guild),
}

func (h *Hub) getOrCreateGuild(name string) *Guild {
	h.mu.Lock()
	defer h.mu.Unlock()

	if guild, ok := h.guilds[name]; ok {
		return guild
	}
	guild := &Guild{
		name:  name,
		rooms: make(map[string]*Room),
		channels: []Channel{
			{Name: "general", Type: "text", Category: "Text-Kanäle"},
			{Name: "random", Type: "text", Category: "Text-Kanäle"},
			{Name: "General Voice", Type: "voice", Category: "Voice-Kanäle"},
			{Name: "Gaming", Type: "voice", Category: "Voice-Kanäle"},
		},
	}
	h.guilds[name] = guild
	return guild
}

func (g *Guild) getOrCreateRoom(name, roomType string) *Room {
	g.mu.Lock()
	defer g.mu.Unlock()

	if room, ok := g.rooms[name]; ok {
		return room
	}
	room := &Room{
		name:         name,
		roomType:     roomType,
		clients:      make(map[*Client]bool),
		voiceClients: make(map[*Client]*VoiceState),
		broadcast:    make(chan []byte),
		register:     make(chan *Client),
		unregister:   make(chan *Client),
		history:      make([]Message, 0, maxHistory),
	}
	g.rooms[name] = room
	go room.run()
	return room
}

func (r *Room) run() {
	for {
		select {
		case client := <-r.register:
			r.mu.Lock()
			r.clients[client] = true
			r.mu.Unlock()
			log.Printf("Client %s joined room %s", client.username, r.name)

			if r.roomType == "text" {
				r.sendHistory(client)
			}
			r.broadcastUserList()

			// Send channel list to new client
			for _, guild := range hub.guilds {
				for _, room := range guild.rooms {
					if room == r {
						msg := Message{
							Type: MessageTypeChannelList,
							Data: guild.channels,
						}
						if data, err := json.Marshal(msg); err == nil {
							client.send <- data
						}
						break
					}
				}
			}

		case client := <-r.unregister:
			r.mu.Lock()
			if _, ok := r.clients[client]; ok {
				delete(r.clients, client)
				delete(r.voiceClients, client)
				close(client.send)
			}
			r.mu.Unlock()
			log.Printf("Client %s left room %s", client.username, r.name)
			r.broadcastUserList()
			r.broadcastVoiceUsers()

		case messageData := <-r.broadcast:
			var msg Message
			if err := json.Unmarshal(messageData, &msg); err == nil {
				r.handleMessage(msg, messageData)
			}
		}
	}
}

func (r *Room) handleMessage(msg Message, messageData []byte) {
	switch msg.Type {
	case MessageTypeNewMessage:
		if r.roomType == "text" {
			r.addMessageToHistory(msg)
		}
		r.broadcastToAll(messageData)

	case MessageTypeJoinVoice:
		r.handleJoinVoice(msg)

	case MessageTypeLeaveVoice:
		r.handleLeaveVoice(msg)

	case MessageTypeWebRTCOffer, MessageTypeWebRTCAnswer, MessageTypeWebRTCICE:
		r.handleWebRTCSignaling(msg, messageData)

	case MessageTypeScreenShare, MessageTypeVideoToggle, MessageTypeAudioToggle:
		r.handleVoiceStateChange(msg)

	case MessageTypeCreateChannel:
		r.handleCreateChannel(msg)

	case MessageTypeDeleteChannel:
		r.handleDeleteChannel(msg)

	default:
		r.broadcastToAll(messageData)
	}
}

func (r *Room) handleJoinVoice(msg Message) {
	// Only allow joining voice if this is a voice channel
	if r.roomType != "voice" {
		return
	}

	r.mu.Lock()
	defer r.mu.Unlock()

	var joiningClient *Client
	for client := range r.clients {
		if client.username == msg.Author {
			// Remove client from any previous voice channel
			r.removeClientFromAllVoiceChannels(client)

			voiceState := &VoiceState{
				UserID:       client.id,
				Username:     client.username,
				AudioMuted:   false,
				VideoEnabled: false,
				ScreenShare:  false,
			}
			r.voiceClients[client] = voiceState
			client.voiceState = voiceState
			client.voiceChannel = r.name
			joiningClient = client
			break
		}
	}

	// Initiate WebRTC connections with existing voice users
	if joiningClient != nil {
		for client := range r.voiceClients {
			if client != joiningClient {
				// Send offer to existing clients to connect to new client
				offerMsg := Message{
					Type:   "webrtc_initiate",
					Target: client.id,
					From:   joiningClient.id,
					Data:   joiningClient.username,
				}
				if data, err := json.Marshal(offerMsg); err == nil {
					select {
					case client.send <- data:
					default:
						close(client.send)
						delete(r.clients, client)
					}
				}
			}
		}
	}

	r.broadcastVoiceUsers()
}

func (r *Room) removeClientFromAllVoiceChannels(targetClient *Client) {
	// Find the guild this room belongs to
	for _, guild := range hub.guilds {
		for _, room := range guild.rooms {
			if room == r {
				// Found our guild, remove client from all voice channels
				for _, otherRoom := range guild.rooms {
					if otherRoom.roomType == "voice" {
						otherRoom.mu.Lock()
						if _, exists := otherRoom.voiceClients[targetClient]; exists {
							delete(otherRoom.voiceClients, targetClient)
							targetClient.voiceState = nil
							targetClient.voiceChannel = ""
							otherRoom.broadcastVoiceUsers()
						}
						otherRoom.mu.Unlock()
					}
				}
				return
			}
		}
	}
}

func (r *Room) handleLeaveVoice(msg Message) {
	// Remove client from all voice channels in the guild
	for client := range r.clients {
		if client.username == msg.Author {
			r.removeClientFromAllVoiceChannels(client)
			break
		}
	}
}

func (r *Room) handleWebRTCSignaling(msg Message, messageData []byte) {
	r.mu.Lock()
	defer r.mu.Unlock()

	// Forward WebRTC signaling to target client
	for client := range r.clients {
		if client.id == msg.Target {
			select {
			case client.send <- messageData:
			default:
				close(client.send)
				delete(r.clients, client)
			}
			break
		}
	}
}

func (r *Room) handleVoiceStateChange(msg Message) {
	r.mu.Lock()
	defer r.mu.Unlock()

	for client, voiceState := range r.voiceClients {
		if client.username == msg.Author {
			switch msg.Type {
			case MessageTypeScreenShare:
				voiceState.ScreenShare = msg.Data.(bool)
			case MessageTypeVideoToggle:
				voiceState.VideoEnabled = msg.Data.(bool)
			case MessageTypeAudioToggle:
				voiceState.AudioMuted = msg.Data.(bool)
			}
			break
		}
	}
	r.broadcastVoiceUsers()
}

func (r *Room) handleCreateChannel(msg Message) {
	// Find the guild this room belongs to
	for _, guild := range hub.guilds {
		for _, room := range guild.rooms {
			if room == r {
				guild.mu.Lock()

				// Parse channel data from message
				if channelData, ok := msg.Data.(map[string]interface{}); ok {
					name := channelData["name"].(string)
					channelType := channelData["type"].(string)
					category := channelData["category"].(string)

					// Check if channel already exists
					exists := false
					for _, ch := range guild.channels {
						if ch.Name == name {
							exists = true
							break
						}
					}

					if !exists {
						// Add new channel
						guild.channels = append(guild.channels, Channel{
							Name:     name,
							Type:     channelType,
							Category: category,
						})

						// Broadcast updated channel list to all clients in guild
						guild.broadcastChannelList()
					}
				}

				guild.mu.Unlock()
				return
			}
		}
	}
}

func (r *Room) handleDeleteChannel(msg Message) {
	// Find the guild this room belongs to
	for _, guild := range hub.guilds {
		for _, room := range guild.rooms {
			if room == r {
				guild.mu.Lock()

				if channelName, ok := msg.Data.(string); ok {
					// Don't allow deletion of default channels
					if channelName == "general" || channelName == "General Voice" {
						guild.mu.Unlock()
						return
					}

					// Remove channel from list
					newChannels := make([]Channel, 0)
					for _, ch := range guild.channels {
						if ch.Name != channelName {
							newChannels = append(newChannels, ch)
						}
					}
					guild.channels = newChannels

					// Broadcast updated channel list to all clients in guild
					guild.broadcastChannelList()
				}

				guild.mu.Unlock()
				return
			}
		}
	}
}

func (g *Guild) broadcastChannelList() {
	msg := Message{
		Type: MessageTypeChannelList,
		Data: g.channels,
	}

	if data, err := json.Marshal(msg); err == nil {
		// Broadcast to all rooms in this guild
		for _, room := range g.rooms {
			room.broadcastToAll(data)
		}
	}
}

func (r *Room) broadcastToAll(messageData []byte) {
	r.mu.Lock()
	defer r.mu.Unlock()

	for client := range r.clients {
		select {
		case client.send <- messageData:
		default:
			close(client.send)
			delete(r.clients, client)
		}
	}
}

func (r *Room) broadcastVoiceUsers() {
	r.mu.Lock()
	voiceUsers := make([]*VoiceState, 0, len(r.voiceClients))
	for _, voiceState := range r.voiceClients {
		voiceUsers = append(voiceUsers, voiceState)
	}
	r.mu.Unlock()

	msg := Message{
		Type: MessageTypeVoiceUsers,
		Data: voiceUsers,
	}

	if data, err := json.Marshal(msg); err == nil {
		// Broadcast to all rooms in the same guild, not just current room
		for _, guild := range hub.guilds {
			for _, room := range guild.rooms {
				if room == r {
					// Found our guild, broadcast to all rooms
					for _, otherRoom := range guild.rooms {
						otherRoom.broadcastToAll(data)
					}
					return
				}
			}
		}
	}
}

func (r *Room) addMessageToHistory(msg Message) {
	r.mu.Lock()
	defer r.mu.Unlock()
	if len(r.history) >= maxHistory {
		r.history = r.history[1:]
	}
	r.history = append(r.history, msg)
}

func (r *Room) sendHistory(client *Client) {
	r.mu.Lock()
	historyCopy := make([]Message, len(r.history))
	copy(historyCopy, r.history)
	r.mu.Unlock()

	historyMsg, _ := json.Marshal(Message{
		Type: MessageTypeHistory,
		Data: historyCopy,
	})
	client.send <- historyMsg
}

func (r *Room) broadcastUserList() {
	// Collect all users from all rooms in the same guild
	allUsers := make(map[string]bool)

	for _, guild := range hub.guilds {
		for _, room := range guild.rooms {
			if room == r {
				// Found our guild, collect users from all rooms
				for _, otherRoom := range guild.rooms {
					otherRoom.mu.Lock()
					for client := range otherRoom.clients {
						allUsers[client.username] = true
					}
					otherRoom.mu.Unlock()
				}
				break
			}
		}
	}

	users := make([]string, 0, len(allUsers))
	for username := range allUsers {
		users = append(users, username)
	}

	msg := Message{
		Type: MessageTypeUserList,
		Data: users,
	}

	if data, err := json.Marshal(msg); err == nil {
		// Broadcast to all rooms in the same guild
		for _, guild := range hub.guilds {
			for _, room := range guild.rooms {
				if room == r {
					// Found our guild, broadcast to all rooms
					for _, otherRoom := range guild.rooms {
						otherRoom.broadcastToAll(data)
					}
					return
				}
			}
		}
	}
}

func (c *Client) readPump() {
	defer func() {
		c.room.unregister <- c
		c.conn.Close()
	}()

	for {
		_, message, err := c.conn.ReadMessage()
		if err != nil {
			log.Printf("Read error: %v", err)
			break
		}

		var msg Message
		if err := json.Unmarshal(message, &msg); err != nil {
			// Fallback for plain text messages
			msg = Message{
				Type:    MessageTypeNewMessage,
				Content: string(message),
				Author:  c.username,
			}
			message, _ = json.Marshal(msg)
		} else {
			msg.Author = c.username
			msg.From = c.id
			message, _ = json.Marshal(msg)
		}

		c.room.broadcast <- message
	}
}

func (c *Client) writePump() {
	defer c.conn.Close()
	for message := range c.send {
		if err := c.conn.WriteMessage(websocket.TextMessage, message); err != nil {
			log.Printf("Write error: %v", err)
			return
		}
	}
}

func handleWebSocket(w http.ResponseWriter, r *http.Request) {
	query := r.URL.Query()
	guildName := query.Get("guild")
	roomName := query.Get("room")
	roomType := query.Get("type")
	username := query.Get("username")

	if guildName == "" || roomName == "" || username == "" {
		http.Error(w, "guild, room, and username are required", http.StatusBadRequest)
		return
	}

	if roomType == "" {
		roomType = "text"
	}

	conn, err := upgrader.Upgrade(w, r, nil)
	if err != nil {
		log.Println("Upgrade error:", err)
		return
	}

	guild := hub.getOrCreateGuild(guildName)
	room := guild.getOrCreateRoom(roomName, roomType)

	client := &Client{
		id:       uuid.New().String(),
		conn:     conn,
		send:     make(chan []byte, 256),
		room:     room,
		username: username,
	}

	room.register <- client

	go client.writePump()
	go client.readPump()
}

func main() {
	fs := http.FileServer(http.Dir("./static"))
	http.Handle("/", fs)
	http.HandleFunc("/ws", handleWebSocket)

	log.Println("Server startet auf :8080")
	if err := http.ListenAndServe(":8080", nil); err != nil {
		log.Fatal("ListenAndServe: ", err)
	}
}
