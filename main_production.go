package main

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/sha256"
	"database/sql"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"github.com/gorilla/mux"
	"github.com/gorilla/websocket"
	"github.com/lib/pq"
	"github.com/redis/go-redis/v9"
	"golang.org/x/crypto/bcrypt"
	"golang.org/x/net/context"
)

// Configuration
type Config struct {
	DBHost      string
	DBUser      string
	DBPassword  string
	DBName      string
	RedisHost   string
	JWTSecret   string
	EncryptKey  string
}

// Database Models
type User struct {
	ID           string    `json:"id" db:"id"`
	Username     string    `json:"username" db:"username"`
	Email        string    `json:"email" db:"email"`
	PasswordHash string    `json:"-" db:"password_hash"`
	AvatarURL    string    `json:"avatar_url" db:"avatar_url"`
	CreatedAt    time.Time `json:"created_at" db:"created_at"`
	IsActive     bool      `json:"is_active" db:"is_active"`
}

type Guild struct {
	ID          string    `json:"id" db:"id"`
	Name        string    `json:"name" db:"name"`
	Description string    `json:"description" db:"description"`
	OwnerID     string    `json:"owner_id" db:"owner_id"`
	IconURL     string    `json:"icon_url" db:"icon_url"`
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
}

type Channel struct {
	ID       string `json:"id" db:"id"`
	GuildID  string `json:"guild_id" db:"guild_id"`
	Name     string `json:"name" db:"name"`
	Type     string `json:"type" db:"type"`
	Category string `json:"category" db:"category"`
	Position int    `json:"position" db:"position"`
}

type Message struct {
	ID                string    `json:"id" db:"id"`
	ChannelID         string    `json:"channel_id" db:"channel_id"`
	UserID            string    `json:"user_id" db:"user_id"`
	Content           string    `json:"content"` // Entschlüsselter Inhalt
	ContentEncrypted  string    `json:"-" db:"content_encrypted"`
	ContentIV         string    `json:"-" db:"content_iv"`
	MessageType       string    `json:"message_type" db:"message_type"`
	CreatedAt         time.Time `json:"created_at" db:"created_at"`
	Author            User      `json:"author"` // Joined data
}

// Application struct
type App struct {
	DB          *sql.DB
	Redis       *redis.Client
	Config      *Config
	Upgrader    websocket.Upgrader
	Clients     map[string]*Client
	Rooms       map[string]*Room
}

type Client struct {
	ID       string
	UserID   string
	Username string
	Conn     *websocket.Conn
	Send     chan []byte
	Room     *Room
}

type Room struct {
	ID      string
	Clients map[*Client]bool
	Hub     *App
}

// JWT Claims
type Claims struct {
	UserID   string `json:"user_id"`
	Username string `json:"username"`
	jwt.RegisteredClaims
}

// Encryption functions
func (app *App) encrypt(plaintext string) (string, string, error) {
	key := []byte(app.Config.EncryptKey)
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", "", err
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", "", err
	}

	nonce := make([]byte, gcm.NonceSize())
	if _, err = io.ReadFull(rand.Reader, nonce); err != nil {
		return "", "", err
	}

	ciphertext := gcm.Seal(nonce, nonce, []byte(plaintext), nil)
	return base64.StdEncoding.EncodeToString(ciphertext), 
		   base64.StdEncoding.EncodeToString(nonce), nil
}

func (app *App) decrypt(ciphertext, nonceStr string) (string, error) {
	key := []byte(app.Config.EncryptKey)
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", err
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", err
	}

	data, err := base64.StdEncoding.DecodeString(ciphertext)
	if err != nil {
		return "", err
	}

	nonce, err := base64.StdEncoding.DecodeString(nonceStr)
	if err != nil {
		return "", err
	}

	plaintext, err := gcm.Open(nil, nonce, data[gcm.NonceSize():], nil)
	if err != nil {
		return "", err
	}

	return string(plaintext), nil
}

// Authentication handlers
func (app *App) registerHandler(w http.ResponseWriter, r *http.Request) {
	var req struct {
		Username string `json:"username"`
		Email    string `json:"email"`
		Password string `json:"password"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request", http.StatusBadRequest)
		return
	}

	// Hash password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		http.Error(w, "Error processing password", http.StatusInternalServerError)
		return
	}

	// Insert user
	userID := uuid.New().String()
	_, err = app.DB.Exec(`
		INSERT INTO users (id, username, email, password_hash) 
		VALUES ($1, $2, $3, $4)`,
		userID, req.Username, req.Email, string(hashedPassword))

	if err != nil {
		if pqErr, ok := err.(*pq.Error); ok && pqErr.Code == "23505" {
			http.Error(w, "Username or email already exists", http.StatusConflict)
			return
		}
		http.Error(w, "Error creating user", http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusCreated)
	json.NewEncoder(w).Encode(map[string]string{"user_id": userID})
}

func (app *App) loginHandler(w http.ResponseWriter, r *http.Request) {
	var req struct {
		Username string `json:"username"`
		Password string `json:"password"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request", http.StatusBadRequest)
		return
	}

	var user User
	err := app.DB.QueryRow(`
		SELECT id, username, email, password_hash, avatar_url, created_at, is_active 
		FROM users WHERE username = $1 AND is_active = true`,
		req.Username).Scan(&user.ID, &user.Username, &user.Email, 
		&user.PasswordHash, &user.AvatarURL, &user.CreatedAt, &user.IsActive)

	if err != nil {
		http.Error(w, "Invalid credentials", http.StatusUnauthorized)
		return
	}

	// Check password
	if err := bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(req.Password)); err != nil {
		http.Error(w, "Invalid credentials", http.StatusUnauthorized)
		return
	}

	// Generate JWT
	claims := &Claims{
		UserID:   user.ID,
		Username: user.Username,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(24 * time.Hour)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte(app.Config.JWTSecret))
	if err != nil {
		http.Error(w, "Error generating token", http.StatusInternalServerError)
		return
	}

	json.NewEncoder(w).Encode(map[string]interface{}{
		"token": tokenString,
		"user":  user,
	})
}

// Initialize application
func NewApp() *App {
	config := &Config{
		DBHost:     getEnv("DB_HOST", "localhost"),
		DBUser:     getEnv("DB_USER", "discord"),
		DBPassword: getEnv("DB_PASSWORD", "password"),
		DBName:     getEnv("DB_NAME", "discord_clone"),
		RedisHost:  getEnv("REDIS_HOST", "localhost:6379"),
		JWTSecret:  getEnv("JWT_SECRET", "your-secret-key"),
		EncryptKey: getEnv("ENCRYPT_KEY", "your-32-byte-encryption-key!!"),
	}

	// Database connection
	dbURL := fmt.Sprintf("postgres://%s:%s@%s/%s?sslmode=disable",
		config.DBUser, config.DBPassword, config.DBHost, config.DBName)
	
	db, err := sql.Open("postgres", dbURL)
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}

	// Redis connection
	rdb := redis.NewClient(&redis.Options{
		Addr: config.RedisHost,
	})

	return &App{
		DB:     db,
		Redis:  rdb,
		Config: config,
		Upgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool { return true },
		},
		Clients: make(map[string]*Client),
		Rooms:   make(map[string]*Room),
	}
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func main() {
	app := NewApp()
	defer app.DB.Close()
	defer app.Redis.Close()

	router := mux.NewRouter()
	
	// Auth routes
	router.HandleFunc("/api/register", app.registerHandler).Methods("POST")
	router.HandleFunc("/api/login", app.loginHandler).Methods("POST")
	
	// Static files
	router.PathPrefix("/").Handler(http.FileServer(http.Dir("./static/")))

	log.Println("Server starting on :8080")
	log.Fatal(http.ListenAndServe(":8080", router))
}
