<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Discord <PERSON>lone - Login</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'gg sans', 'Noto Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .auth-container {
            background: #36393f;
            border-radius: 8px;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.24);
            padding: 32px;
            width: 100%;
            max-width: 480px;
        }
        
        .auth-header {
            text-align: center;
            margin-bottom: 32px;
        }
        
        .auth-header h1 {
            color: #ffffff;
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .auth-header p {
            color: #b9bbbe;
            font-size: 16px;
        }
        
        .auth-tabs {
            display: flex;
            margin-bottom: 24px;
            background: #2f3136;
            border-radius: 4px;
            padding: 4px;
        }
        
        .auth-tab {
            flex: 1;
            padding: 12px;
            text-align: center;
            background: none;
            border: none;
            color: #b9bbbe;
            cursor: pointer;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.15s ease;
        }
        
        .auth-tab.active {
            background: #5865f2;
            color: #ffffff;
        }
        
        .auth-form {
            display: none;
        }
        
        .auth-form.active {
            display: block;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            color: #b9bbbe;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.02em;
            margin-bottom: 8px;
        }
        
        .form-input {
            width: 100%;
            background: #202225;
            border: 1px solid #202225;
            border-radius: 3px;
            color: #dcddde;
            font-size: 16px;
            padding: 10px 12px;
            outline: none;
            transition: border-color 0.15s ease;
        }
        
        .form-input:focus {
            border-color: #5865f2;
        }
        
        .form-button {
            width: 100%;
            background: #5865f2;
            border: none;
            border-radius: 3px;
            color: #ffffff;
            font-size: 16px;
            font-weight: 500;
            padding: 12px;
            cursor: pointer;
            transition: background-color 0.17s ease;
            margin-top: 20px;
        }
        
        .form-button:hover {
            background: #4752c4;
        }
        
        .form-button:disabled {
            background: #4f545c;
            cursor: not-allowed;
        }
        
        .error-message {
            background: #ed4245;
            color: #ffffff;
            padding: 12px;
            border-radius: 4px;
            margin-bottom: 20px;
            font-size: 14px;
            display: none;
        }
        
        .success-message {
            background: #3ba55c;
            color: #ffffff;
            padding: 12px;
            border-radius: 4px;
            margin-bottom: 20px;
            font-size: 14px;
            display: none;
        }
        
        .loading {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid #ffffff;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s ease-in-out infinite;
            margin-right: 8px;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .demo-info {
            background: #2f3136;
            border-radius: 4px;
            padding: 16px;
            margin-top: 24px;
            border-left: 4px solid #faa61a;
        }
        
        .demo-info h3 {
            color: #faa61a;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .demo-info p {
            color: #b9bbbe;
            font-size: 12px;
            line-height: 1.4;
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <div class="auth-header">
            <h1>Willkommen zurück!</h1>
            <p>Wir haben dich vermisst!</p>
        </div>
        
        <div class="auth-tabs">
            <button class="auth-tab active" data-tab="login">Anmelden</button>
            <button class="auth-tab" data-tab="register">Registrieren</button>
        </div>
        
        <div class="error-message" id="error-message"></div>
        <div class="success-message" id="success-message"></div>
        
        <!-- Login Form -->
        <form class="auth-form active" id="login-form">
            <div class="form-group">
                <label class="form-label" for="login-username">Benutzername</label>
                <input class="form-input" type="text" id="login-username" required>
            </div>
            <div class="form-group">
                <label class="form-label" for="login-password">Passwort</label>
                <input class="form-input" type="password" id="login-password" required>
            </div>
            <button class="form-button" type="submit" id="login-button">
                Anmelden
            </button>
        </form>
        
        <!-- Register Form -->
        <form class="auth-form" id="register-form">
            <div class="form-group">
                <label class="form-label" for="register-email">E-Mail</label>
                <input class="form-input" type="email" id="register-email" required>
            </div>
            <div class="form-group">
                <label class="form-label" for="register-username">Benutzername</label>
                <input class="form-input" type="text" id="register-username" required>
            </div>
            <div class="form-group">
                <label class="form-label" for="register-password">Passwort</label>
                <input class="form-input" type="password" id="register-password" required>
            </div>
            <button class="form-button" type="submit" id="register-button">
                Konto erstellen
            </button>
        </form>
        
        <div class="demo-info">
            <h3>Demo-Modus</h3>
            <p>Dies ist eine Demo-Version. Alle Daten werden verschlüsselt gespeichert. 
               Für Tests können Sie beliebige Anmeldedaten verwenden.</p>
        </div>
    </div>

    <script>
        // Tab switching
        document.querySelectorAll('.auth-tab').forEach(tab => {
            tab.addEventListener('click', () => {
                const targetTab = tab.dataset.tab;
                
                // Update active tab
                document.querySelectorAll('.auth-tab').forEach(t => t.classList.remove('active'));
                tab.classList.add('active');
                
                // Update active form
                document.querySelectorAll('.auth-form').forEach(form => form.classList.remove('active'));
                document.getElementById(`${targetTab}-form`).classList.add('active');
                
                // Clear messages
                hideMessages();
            });
        });

        // Form submissions
        document.getElementById('login-form').addEventListener('submit', handleLogin);
        document.getElementById('register-form').addEventListener('submit', handleRegister);

        async function handleLogin(e) {
            e.preventDefault();
            
            const button = document.getElementById('login-button');
            const username = document.getElementById('login-username').value;
            const password = document.getElementById('login-password').value;
            
            setLoading(button, true);
            hideMessages();
            
            try {
                const response = await fetch('/api/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password }),
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    // Store token
                    localStorage.setItem('auth_token', data.token);
                    localStorage.setItem('user_data', JSON.stringify(data.user));
                    
                    showSuccess('Anmeldung erfolgreich! Weiterleitung...');
                    
                    // Redirect to main app
                    setTimeout(() => {
                        window.location.href = '/';
                    }, 1000);
                } else {
                    showError(data.error || 'Anmeldung fehlgeschlagen');
                }
            } catch (error) {
                showError('Verbindungsfehler. Bitte versuchen Sie es erneut.');
            } finally {
                setLoading(button, false);
            }
        }

        async function handleRegister(e) {
            e.preventDefault();
            
            const button = document.getElementById('register-button');
            const email = document.getElementById('register-email').value;
            const username = document.getElementById('register-username').value;
            const password = document.getElementById('register-password').value;
            
            setLoading(button, true);
            hideMessages();
            
            try {
                const response = await fetch('/api/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email, username, password }),
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showSuccess('Registrierung erfolgreich! Sie können sich jetzt anmelden.');
                    
                    // Switch to login tab
                    setTimeout(() => {
                        document.querySelector('[data-tab="login"]').click();
                        document.getElementById('login-username').value = username;
                    }, 1500);
                } else {
                    showError(data.error || 'Registrierung fehlgeschlagen');
                }
            } catch (error) {
                showError('Verbindungsfehler. Bitte versuchen Sie es erneut.');
            } finally {
                setLoading(button, false);
            }
        }

        function setLoading(button, loading) {
            if (loading) {
                button.disabled = true;
                button.innerHTML = '<span class="loading"></span>Laden...';
            } else {
                button.disabled = false;
                button.innerHTML = button.id.includes('login') ? 'Anmelden' : 'Konto erstellen';
            }
        }

        function showError(message) {
            const errorEl = document.getElementById('error-message');
            errorEl.textContent = message;
            errorEl.style.display = 'block';
        }

        function showSuccess(message) {
            const successEl = document.getElementById('success-message');
            successEl.textContent = message;
            successEl.style.display = 'block';
        }

        function hideMessages() {
            document.getElementById('error-message').style.display = 'none';
            document.getElementById('success-message').style.display = 'none';
        }

        // Check if already logged in
        if (localStorage.getItem('auth_token')) {
            window.location.href = '/';
        }
    </script>
</body>
</html>
