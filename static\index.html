<!-- static/index.html -->
<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <title>Discord Clone</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'gg sans', 'Noto Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background: #313338;
            color: #dbdee1;
            height: 100vh;
            overflow: hidden;
        }

        /* Join Screen */
        #join-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #5865f2 0%, #3ba55c 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        #join-box {
            background: #313338;
            padding: 32px;
            border-radius: 8px;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.24);
            text-align: center;
            min-width: 400px;
        }

        #join-box h2 {
            color: #f2f3f5;
            margin-bottom: 8px;
            font-size: 24px;
            font-weight: 600;
        }

        #join-box p {
            color: #b5bac1;
            margin-bottom: 20px;
            font-size: 16px;
        }

        #join-box input {
            display: block;
            margin: 16px auto;
            padding: 10px 12px;
            width: 100%;
            border: none;
            background: #1e1f22;
            color: #dbdee1;
            border-radius: 3px;
            font-size: 16px;
            outline: none;
            transition: background-color 0.15s ease;
        }

        #join-box input:focus {
            background: #2b2d31;
        }

        #join-box button {
            padding: 12px 24px;
            background: #5865f2;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            width: 100%;
            margin-top: 20px;
            transition: background-color 0.17s ease;
        }

        #join-box button:hover {
            background: #4752c4;
        }

        .hidden {
            display: none !important;
        }

        /* Main App Layout */
        #app-container {
            display: flex;
            width: 100%;
            height: 100vh;
        }

        /* Guild Sidebar */
        #guilds-bar {
            width: 72px;
            background: #1e1f22;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 12px 0;
            gap: 8px;
        }

        .guild-icon {
            width: 48px;
            height: 48px;
            background: #313338;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.15s ease;
            color: #80848e;
            font-weight: 600;
            font-size: 18px;
        }

        .guild-icon:hover {
            border-radius: 16px;
            background: #5865f2;
            color: #fff;
        }

        .guild-icon.active {
            border-radius: 16px;
            background: #5865f2;
            color: #fff;
        }

        .guild-separator {
            width: 32px;
            height: 2px;
            background: #35373c;
            border-radius: 1px;
            margin: 4px 0;
        }

        /* Channels Sidebar */
        #channels-bar {
            width: 240px;
            background: #2b2d31;
            display: flex;
            flex-direction: column;
        }

        #channels-header {
            padding: 12px 16px;
            border-bottom: 1px solid #1e1f22;
            font-weight: 600;
            font-size: 16px;
            color: #f2f3f5;
            background: #2b2d31;
            box-shadow: 0 1px 0 rgba(4, 4, 5, 0.2), 0 1.5px 0 rgba(6, 6, 7, 0.05), 0 2px 0 rgba(4, 4, 5, 0.05);
        }

        #channels-list {
            list-style: none;
            padding: 16px 8px;
            flex: 1;
            overflow-y: auto;
        }

        .channel-category {
            color: #949ba4;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.02em;
            margin: 16px 8px 0 8px;
            padding: 0 8px;
        }

        .channel-category:first-child {
            margin-top: 0;
        }

        #channels-list li {
            padding: 1px 8px;
            margin: 1px 0;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            color: #949ba4;
            font-weight: 500;
            transition: all 0.15s ease;
        }

        #channels-list li:hover {
            background: #35373c;
            color: #dbdee1;
        }

        #channels-list li.active {
            background: #404249;
            color: #f2f3f5;
        }

        #channels-list li .channel-icon {
            margin-right: 6px;
            font-size: 20px;
            width: 20px;
            text-align: center;
        }

        #channels-list li.text .channel-icon::before {
            content: "#";
            color: #80848e;
        }

        #channels-list li.voice .channel-icon::before {
            content: "🔊";
        }

        #channels-list li.active .channel-icon::before {
            color: #f2f3f5;
        }

        /* User Area at bottom of channels */
        #user-area {
            background: #232428;
            padding: 8px;
            display: flex;
            align-items: center;
            border-top: 1px solid #1e1f22;
        }

        #user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #5865f2;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            margin-right: 8px;
        }

        #user-info {
            flex: 1;
        }

        #user-name {
            font-size: 14px;
            font-weight: 600;
            color: #f2f3f5;
        }

        #user-status {
            font-size: 12px;
            color: #949ba4;
        }

        #user-controls {
            display: flex;
            gap: 4px;
        }

        .user-control-btn {
            width: 32px;
            height: 32px;
            border: none;
            background: none;
            color: #b5bac1;
            cursor: pointer;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.15s ease;
        }

        .user-control-btn:hover {
            background: #35373c;
            color: #dbdee1;
        }

        /* Main Content Area */
        #main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: #313338;
        }

        /* Chat Header */
        #chat-header {
            height: 48px;
            background: #313338;
            border-bottom: 1px solid #1e1f22;
            display: flex;
            align-items: center;
            padding: 0 16px;
            box-shadow: 0 1px 0 rgba(4, 4, 5, 0.2), 0 1.5px 0 rgba(6, 6, 7, 0.05), 0 2px 0 rgba(4, 4, 5, 0.05);
        }

        #chat-header .channel-icon {
            color: #80848e;
            margin-right: 8px;
            font-size: 24px;
        }

        #chat-header .channel-name {
            font-size: 16px;
            font-weight: 600;
            color: #f2f3f5;
        }

        #chat-header .channel-topic {
            margin-left: 8px;
            color: #949ba4;
            font-size: 14px;
        }

        /* Chat Area */
        #chat-area {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        #messages {
            flex: 1;
            padding: 16px 0;
            overflow-y: auto;
            background: #313338;
        }

        .message {
            padding: 2px 16px;
            margin-bottom: 4px;
            position: relative;
            display: flex;
            align-items: flex-start;
            min-height: 44px;
        }

        .message:hover {
            background: #2e3035;
        }

        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #5865f2;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            margin-right: 16px;
            margin-top: 2px;
            flex-shrink: 0;
        }

        .message-content {
            flex: 1;
            min-width: 0;
        }

        .message-header {
            display: flex;
            align-items: baseline;
            margin-bottom: 2px;
        }

        .message-author {
            font-weight: 600;
            color: #f2f3f5;
            margin-right: 8px;
            cursor: pointer;
        }

        .message-timestamp {
            font-size: 12px;
            color: #949ba4;
            margin-left: 8px;
        }

        .message-text {
            color: #dbdee1;
            line-height: 1.375;
            word-wrap: break-word;
        }

        /* Message Input */
        #message-input-wrapper {
            padding: 0 16px 24px 16px;
        }

        #message-input {
            width: 100%;
            background: #383a40;
            border: none;
            padding: 11px 16px;
            border-radius: 8px;
            color: #dbdee1;
            font-size: 16px;
            outline: none;
            resize: none;
            max-height: 144px;
        }

        #message-input::placeholder {
            color: #6d6f78;
        }

        /* Voice Area */
        #voice-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 16px;
            background: #313338;
        }

        #voice-controls {
            display: flex;
            gap: 8px;
            margin-bottom: 16px;
            padding: 16px;
            background: #2b2d31;
            border-radius: 8px;
        }

        #voice-controls button {
            padding: 8px 16px;
            background: #4e5058;
            color: #dbdee1;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.15s ease;
        }

        #voice-controls button:hover {
            background: #5c5e66;
        }

        #voice-controls button.active {
            background: #248046;
            color: #fff;
        }

        #voice-controls button.muted {
            background: #da373c;
            color: #fff;
        }

        #video-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 16px;
            flex: 1;
        }

        .video-container {
            background: #1e1f22;
            border-radius: 8px;
            overflow: hidden;
            position: relative;
            min-height: 240px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .video-container video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .video-container .username {
            position: absolute;
            bottom: 8px;
            left: 8px;
            background: rgba(0, 0, 0, 0.8);
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 600;
            color: #f2f3f5;
        }

        .video-container .audio-only {
            font-size: 64px;
            color: #80848e;
        }

        /* Members Sidebar */
        #members-bar {
            width: 240px;
            background: #2b2d31;
            display: flex;
            flex-direction: column;
            border-left: 1px solid #1e1f22;
        }

        #members-list {
            padding: 16px 8px;
            flex: 1;
            overflow-y: auto;
        }

        .member-category {
            color: #949ba4;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.02em;
            margin: 16px 8px 8px 8px;
            padding: 0 8px;
        }

        .member-category:first-child {
            margin-top: 0;
        }

        .member {
            display: flex;
            align-items: center;
            padding: 1px 8px;
            margin: 1px 0;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.15s ease;
        }

        .member:hover {
            background: #35373c;
        }

        .member-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #5865f2;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            margin-right: 12px;
            font-size: 14px;
        }

        .member-info {
            flex: 1;
            min-width: 0;
        }

        .member-name {
            font-size: 16px;
            font-weight: 500;
            color: #949ba4;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .member.online .member-name {
            color: #f2f3f5;
        }

        .member-status {
            display: flex;
            align-items: center;
            gap: 4px;
            margin-left: auto;
        }

        .voice-indicator {
            font-size: 16px;
        }
    </style>
</head>
<body>

<div id="join-screen">
    <div id="join-box">
        <h2>Willkommen bei Discord!</h2>
        <p>Tritt deinem Server bei</p>
        <input type="text" id="username-input" placeholder="Benutzername eingeben">
        <input type="text" id="guild-name-input" placeholder="Server-Name (z.B. 'Gaming')" value="Gaming">
        <button id="join-button">Server beitreten</button>
    </div>
</div>

<div id="app-container" class="hidden">
    <!-- Guild Sidebar -->
    <div id="guilds-bar">
        <div class="guild-icon active">G</div>
        <div class="guild-separator"></div>
        <div class="guild-icon">+</div>
    </div>

    <!-- Channels Sidebar -->
    <div id="channels-bar">
        <div id="channels-header">Gaming Server</div>
        <ul id="channels-list"></ul>

        <!-- User Area -->
        <div id="user-area">
            <div id="user-avatar"></div>
            <div id="user-info">
                <div id="user-name"></div>
                <div id="user-status">Online</div>
            </div>
            <div id="user-controls">
                <button class="user-control-btn" title="Mikrofon stumm schalten">🎤</button>
                <button class="user-control-btn" title="Kopfhörer stumm schalten">🎧</button>
                <button class="user-control-btn" title="Einstellungen">⚙️</button>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div id="main-content">
        <!-- Chat Header -->
        <div id="chat-header">
            <span class="channel-icon">#</span>
            <span class="channel-name" id="current-channel-name">general</span>
            <span class="channel-topic">| Allgemeiner Chat für alle Themen</span>
        </div>

        <!-- Chat Area -->
        <div id="chat-area">
            <div id="messages"></div>
            <div id="message-input-wrapper">
                <input type="text" id="message-input" placeholder="Nachricht in #general">
            </div>
        </div>

        <!-- Voice Area -->
        <div id="voice-area" class="hidden">
            <div id="voice-controls">
                <button id="join-voice-btn">Voice-Kanal beitreten</button>
                <button id="mute-btn">Mikrofon</button>
                <button id="video-btn">Kamera</button>
                <button id="screen-share-btn">Bildschirm teilen</button>
                <button id="leave-voice-btn" class="hidden">Voice verlassen</button>
            </div>
            <div id="video-grid"></div>
        </div>
    </div>

    <!-- Members Sidebar -->
    <div id="members-bar">
        <div id="members-list">
            <div class="member-category">Online — <span id="online-count">0</span></div>
            <div id="online-members"></div>
            <div class="member-category">Voice — <span id="voice-count">0</span></div>
            <div id="voice-members"></div>
        </div>
    </div>
</div>

<script>
    // UI Elements
    const joinScreen = document.getElementById('join-screen');
    const appContainer = document.getElementById('app-container');
    const joinButton = document.getElementById('join-button');
    const usernameInput = document.getElementById('username-input');
    const guildNameInput = document.getElementById('guild-name-input');
    const channelsList = document.getElementById('channels-list');
    const messagesDiv = document.getElementById('messages');
    const messageInput = document.getElementById('message-input');
    const chatArea = document.getElementById('chat-area');
    const voiceArea = document.getElementById('voice-area');
    const videoGrid = document.getElementById('video-grid');
    const currentChannelName = document.getElementById('current-channel-name');
    const chatHeader = document.getElementById('chat-header');
    const onlineMembers = document.getElementById('online-members');
    const voiceMembers = document.getElementById('voice-members');
    const onlineCount = document.getElementById('online-count');
    const voiceCount = document.getElementById('voice-count');
    const userAvatar = document.getElementById('user-avatar');
    const userName = document.getElementById('user-name');

    // Voice controls
    const joinVoiceBtn = document.getElementById('join-voice-btn');
    const leaveVoiceBtn = document.getElementById('leave-voice-btn');
    const muteBtn = document.getElementById('mute-btn');
    const videoBtn = document.getElementById('video-btn');
    const screenShareBtn = document.getElementById('screen-share-btn');

    let ws;
    let currentGuild, currentRoom, currentUsername, currentRoomType;
    let localStream;
    let peerConnections = new Map();
    let isInVoice = false;
    let isMuted = false;
    let isVideoEnabled = false;
    let isScreenSharing = false;

    const channels = [
        { name: 'general', type: 'text', category: 'Text-Kanäle' },
        { name: 'random', type: 'text', category: 'Text-Kanäle' },
        { name: 'ankündigungen', type: 'text', category: 'Text-Kanäle' },
        { name: 'General Voice', type: 'voice', category: 'Voice-Kanäle' },
        { name: 'Gaming', type: 'voice', category: 'Voice-Kanäle' },
        { name: 'Music', type: 'voice', category: 'Voice-Kanäle' }
    ];

    const rtcConfiguration = {
        iceServers: [
            { urls: 'stun:stun.l.google.com:19302' },
            { urls: 'stun:stun1.l.google.com:19302' }
        ]
    };

    function connect() {
        if (ws) {
            ws.close();
        }

        const wsUrl = `ws://${window.location.host}/ws?guild=${encodeURIComponent(currentGuild)}&room=${encodeURIComponent(currentRoom)}&type=${currentRoomType}&username=${encodeURIComponent(currentUsername)}`;
        console.log('Connecting to:', wsUrl);
        
        ws = new WebSocket(wsUrl);

        ws.onopen = () => {
            console.log(`Verbunden mit ${currentGuild}/${currentRoom} (${currentRoomType})`);
            if (currentRoomType === 'text') {
                messagesDiv.innerHTML = '';
            }
        };

        ws.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);
                handleMessage(data);
            } catch (error) {
                console.error('Error parsing message:', error);
            }
        };

        ws.onclose = () => {
            console.log('Verbindung getrennt.');
        };
        
        ws.onerror = (error) => {
            console.error('WebSocket-Fehler:', error);
        };
    }

    function handleMessage(data) {
        console.log('Received message:', data);
        
        switch (data.type) {
            case 'history':
                if (data.data) {
                    data.data.forEach(msg => displayMessage(msg));
                }
                break;
            case 'new_message':
                displayMessage(data);
                break;
            case 'user_list':
                updateUserList(data.data);
                break;
            case 'voice_users':
                updateVoiceUserList(data.data);
                break;
            case 'webrtc_initiate':
                handleWebRTCInitiate(data);
                break;
            case 'webrtc_offer':
                handleWebRTCOffer(data);
                break;
            case 'webrtc_answer':
                handleWebRTCAnswer(data);
                break;
            case 'webrtc_ice':
                handleWebRTCICE(data);
                break;
        }
    }

    function displayMessage(msg) {
        const msgElement = document.createElement('div');
        msgElement.className = 'message';

        const avatar = document.createElement('div');
        avatar.className = 'message-avatar';
        avatar.textContent = msg.author.charAt(0).toUpperCase();

        const content = document.createElement('div');
        content.className = 'message-content';

        const header = document.createElement('div');
        header.className = 'message-header';

        const author = document.createElement('span');
        author.className = 'message-author';
        author.textContent = msg.author;

        const timestamp = document.createElement('span');
        timestamp.className = 'message-timestamp';
        timestamp.textContent = new Date().toLocaleTimeString('de-DE', {
            hour: '2-digit',
            minute: '2-digit'
        });

        const text = document.createElement('div');
        text.className = 'message-text';
        text.textContent = msg.content;

        header.appendChild(author);
        header.appendChild(timestamp);
        content.appendChild(header);
        content.appendChild(text);
        msgElement.appendChild(avatar);
        msgElement.appendChild(content);

        messagesDiv.appendChild(msgElement);
        messagesDiv.scrollTop = messagesDiv.scrollHeight;
    }

    function updateUserList(users) {
        onlineMembers.innerHTML = '';
        if (users) {
            onlineCount.textContent = users.length;
            users.forEach(user => {
                const memberElement = document.createElement('div');
                memberElement.className = 'member online';

                const avatar = document.createElement('div');
                avatar.className = 'member-avatar';
                avatar.textContent = user.charAt(0).toUpperCase();

                const info = document.createElement('div');
                info.className = 'member-info';

                const name = document.createElement('div');
                name.className = 'member-name';
                name.textContent = user;

                info.appendChild(name);
                memberElement.appendChild(avatar);
                memberElement.appendChild(info);

                if (user === currentUsername) {
                    name.style.fontWeight = '600';
                    name.style.color = '#f2f3f5';
                }

                onlineMembers.appendChild(memberElement);
            });
        }
    }

    function updateVoiceUserList(voiceUsers) {
        voiceMembers.innerHTML = '';
        if (voiceUsers) {
            voiceCount.textContent = voiceUsers.length;
            voiceUsers.forEach(user => {
                const memberElement = document.createElement('div');
                memberElement.className = 'member online';

                const avatar = document.createElement('div');
                avatar.className = 'member-avatar';
                avatar.textContent = user.username.charAt(0).toUpperCase();

                const info = document.createElement('div');
                info.className = 'member-info';

                const name = document.createElement('div');
                name.className = 'member-name';
                name.textContent = user.username;

                const status = document.createElement('div');
                status.className = 'member-status';

                if (user.audio_muted) {
                    const muteIcon = document.createElement('span');
                    muteIcon.className = 'voice-indicator';
                    muteIcon.textContent = '🔇';
                    status.appendChild(muteIcon);
                }

                if (user.video_enabled) {
                    const videoIcon = document.createElement('span');
                    videoIcon.className = 'voice-indicator';
                    videoIcon.textContent = '📹';
                    status.appendChild(videoIcon);
                }

                if (user.screen_share) {
                    const screenIcon = document.createElement('span');
                    screenIcon.className = 'voice-indicator';
                    screenIcon.textContent = '🖥️';
                    status.appendChild(screenIcon);
                }

                info.appendChild(name);
                memberElement.appendChild(avatar);
                memberElement.appendChild(info);
                memberElement.appendChild(status);

                voiceMembers.appendChild(memberElement);
            });
        }
    }

    function renderChannels() {
        channelsList.innerHTML = '';

        // Group channels by category
        const categories = {};
        channels.forEach(channel => {
            if (!categories[channel.category]) {
                categories[channel.category] = [];
            }
            categories[channel.category].push(channel);
        });

        // Render each category
        Object.keys(categories).forEach(categoryName => {
            const categoryElement = document.createElement('div');
            categoryElement.className = 'channel-category';
            categoryElement.textContent = categoryName;
            channelsList.appendChild(categoryElement);

            categories[categoryName].forEach(channel => {
                const li = document.createElement('li');
                li.className = channel.type;
                li.dataset.channel = channel.name;
                li.dataset.type = channel.type;

                const icon = document.createElement('span');
                icon.className = 'channel-icon';

                const name = document.createElement('span');
                name.textContent = channel.name;

                li.appendChild(icon);
                li.appendChild(name);

                if (channel.name === currentRoom) {
                    li.classList.add('active');
                }

                li.onclick = () => switchChannel(channel.name, channel.type);
                channelsList.appendChild(li);
            });
        });
    }

    function switchChannel(channelName, channelType) {
        if (currentRoom === channelName) return;

        // Leave voice if switching from voice channel
        if (currentRoomType === 'voice' && isInVoice) {
            leaveVoice();
        }

        currentRoom = channelName;
        currentRoomType = channelType;

        // Update channel name in header
        currentChannelName.textContent = channelName;

        // Update header icon and placeholder
        const headerIcon = chatHeader.querySelector('.channel-icon');
        if (channelType === 'text') {
            headerIcon.textContent = '#';
            messageInput.placeholder = `Nachricht in #${channelName}`;
            chatArea.classList.remove('hidden');
            voiceArea.classList.add('hidden');
        } else {
            headerIcon.textContent = '🔊';
            chatArea.classList.add('hidden');
            voiceArea.classList.remove('hidden');
        }

        renderChannels();
        connect();
    }

    async function joinVoice() {
        try {
            // Try to get audio, but don't require it
            let constraints = { audio: true, video: false };
            
            try {
                localStream = await navigator.mediaDevices.getUserMedia(constraints);
            } catch (audioError) {
                console.log('Audio not available, joining voice without microphone');
                // Create a silent audio track
                localStream = new MediaStream();
            }
            
            isInVoice = true;
            joinVoiceBtn.classList.add('hidden');
            leaveVoiceBtn.classList.remove('hidden');
            
            sendMessage({
                type: 'join_voice'
            });
            
            addVideoElement(currentUsername, localStream, true);
            
        } catch (error) {
            console.error('Fehler beim Beitreten des Voice-Channels:', error);
            // Still allow joining without microphone
            isInVoice = true;
            joinVoiceBtn.classList.add('hidden');
            leaveVoiceBtn.classList.remove('hidden');
            
            sendMessage({
                type: 'join_voice'
            });
            
            addVideoElement(currentUsername, null, true);
        }
    }

    function leaveVoice() {
        if (localStream) {
            localStream.getTracks().forEach(track => track.stop());
            localStream = null;
        }
        
        // Close all peer connections
        peerConnections.forEach(pc => pc.close());
        peerConnections.clear();
        
        // Clear video grid
        videoGrid.innerHTML = '';
        
        isInVoice = false;
        isVideoEnabled = false;
        isScreenSharing = false;
        
        joinVoiceBtn.classList.remove('hidden');
        leaveVoiceBtn.classList.add('hidden');
        videoBtn.classList.remove('active');
        screenShareBtn.classList.remove('active');
        
        sendMessage({
            type: 'leave_voice'
        });
    }

    async function toggleVideo() {
        if (!isInVoice) return;
        
        try {
            if (!isVideoEnabled) {
                const videoStream = await navigator.mediaDevices.getUserMedia({ 
                    video: true, 
                    audio: true 
                });
                
                localStream = videoStream;
                isVideoEnabled = true;
                videoBtn.classList.add('active');
                
                updateVideoElement(currentUsername, localStream);
                
            } else {
                localStream.getVideoTracks().forEach(track => track.stop());
                try {
                    localStream = await navigator.mediaDevices.getUserMedia({ audio: true });
                } catch (e) {
                    localStream = new MediaStream();
                }
                
                isVideoEnabled = false;
                videoBtn.classList.remove('active');
                
                updateVideoElement(currentUsername, localStream);
            }
            
            updatePeerConnections();
            
            sendMessage({
                type: 'video_toggle',
                data: isVideoEnabled
            });
            
        } catch (error) {
            console.error('Fehler beim Video-Toggle:', error);
        }
    }

    async function toggleScreenShare() {
        if (!isInVoice) return;
        
        try {
            if (!isScreenSharing) {
                const screenStream = await navigator.mediaDevices.getDisplayMedia({ 
                    video: true, 
                    audio: true 
                });
                
                localStream = screenStream;
                isScreenSharing = true;
                screenShareBtn.classList.add('active');
                
                screenStream.getVideoTracks()[0].onended = () => {
                    toggleScreenShare();
                };
                
            } else {
                localStream.getTracks().forEach(track => track.stop());
                try {
                    localStream = await navigator.mediaDevices.getUserMedia({ 
                        audio: true, 
                        video: isVideoEnabled 
                    });
                } catch (e) {
                    localStream = new MediaStream();
                }
                
                isScreenSharing = false;
                screenShareBtn.classList.remove('active');
            }
            
            updateVideoElement(currentUsername, localStream);
            updatePeerConnections();
            
            sendMessage({
                type: 'screen_share',
                data: isScreenSharing
            });
            
        } catch (error) {
            console.error('Fehler beim Screen-Share:', error);
        }
    }

    function toggleMute() {
        if (!localStream) return;
        
        const audioTrack = localStream.getAudioTracks()[0];
        if (audioTrack) {
            audioTrack.enabled = !audioTrack.enabled;
            isMuted = !audioTrack.enabled;
            
            if (isMuted) {
                muteBtn.classList.add('muted');
                muteBtn.textContent = 'Stumm (An)';
            } else {
                muteBtn.classList.remove('muted');
                muteBtn.textContent = 'Stumm';
            }
            
            sendMessage({
                type: 'audio_toggle',
                data: isMuted
            });
        }
    }

    function addVideoElement(username, stream, isLocal = false) {
        // Remove existing element if it exists
        removeVideoElement(username);
        
        const container = document.createElement('div');
        container.className = 'video-container';
        container.id = `video-${username}`;
        
        if (stream && stream.getVideoTracks().length > 0) {
            const video = document.createElement('video');
            video.srcObject = stream;
            video.autoplay = true;
            video.muted = isLocal;
            container.appendChild(video);
        } else {
            // Audio-only user
            const audioIcon = document.createElement('div');
            audioIcon.className = 'audio-only';
            audioIcon.textContent = '🎤';
            container.appendChild(audioIcon);
        }
        
        const label = document.createElement('div');
        label.className = 'username';
        label.textContent = username;
        
        container.appendChild(label);
        videoGrid.appendChild(container);
    }

    function updateVideoElement(username, stream) {
        const container = document.getElementById(`video-${username}`);
        if (container) {
            // Clear container
            container.innerHTML = '';
            
            if (stream && stream.getVideoTracks().length > 0) {
                const video = document.createElement('video');
                video.srcObject = stream;
                video.autoplay = true;
                video.muted = username === currentUsername;
                container.appendChild(video);
            } else {
                const audioIcon = document.createElement('div');
                audioIcon.className = 'audio-only';
                audioIcon.textContent = '🎤';
                container.appendChild(audioIcon);
            }
            
            const label = document.createElement('div');
            label.className = 'username';
            label.textContent = username;
            container.appendChild(label);
        }
    }

    function removeVideoElement(username) {
        const container = document.getElementById(`video-${username}`);
        if (container) {
            container.remove();
        }
    }

    async function handleWebRTCInitiate(data) {
        if (isInVoice) {
            const pc = await createPeerConnection(data.from);
            const offer = await pc.createOffer();
            await pc.setLocalDescription(offer);
            
            sendMessage({
                type: 'webrtc_offer',
                target: data.from,
                data: offer
            });
        }
    }

    async function createPeerConnection(targetUserId) {
        const pc = new RTCPeerConnection(rtcConfiguration);
        peerConnections.set(targetUserId, pc);
        
        if (localStream) {
            localStream.getTracks().forEach(track => {
                pc.addTrack(track, localStream);
            });
        }
        
        pc.ontrack = (event) => {
            const remoteStream = event.streams[0];
            // Find username by user ID (simplified - in real app you'd track this)
            addVideoElement(`User-${targetUserId.slice(-4)}`, remoteStream);
        };
        
        pc.onicecandidate = (event) => {
            if (event.candidate) {
                sendMessage({
                    type: 'webrtc_ice',
                    target: targetUserId,
                    data: event.candidate
                });
            }
        };
        
        return pc;
    }

    async function handleWebRTCOffer(data) {
        const pc = await createPeerConnection(data.from);
        await pc.setRemoteDescription(data.data);
        
        const answer = await pc.createAnswer();
        await pc.setLocalDescription(answer);
        
        sendMessage({
            type: 'webrtc_answer',
            target: data.from,
            data: answer
        });
    }

    async function handleWebRTCAnswer(data) {
        const pc = peerConnections.get(data.from);
        if (pc) {
            await pc.setRemoteDescription(data.data);
        }
    }

    async function handleWebRTCICE(data) {
        const pc = peerConnections.get(data.from);
        if (pc) {
            await pc.addIceCandidate(data.data);
        }
    }

    function updatePeerConnections() {
        peerConnections.forEach(async (pc, userId) => {
            const senders = pc.getSenders();
            senders.forEach(sender => {
                if (sender.track) {
                    pc.removeTrack(sender);
                }
            });
            
            if (localStream) {
                localStream.getTracks().forEach(track => {
                    pc.addTrack(track, localStream);
                });
            }
        });
    }

    function sendMessage(message) {
        if (ws && ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify(message));
        } else {
            console.error('WebSocket not connected');
        }
    }

    // Event Listeners
    joinButton.onclick = () => {
        currentUsername = usernameInput.value.trim();
        currentGuild = guildNameInput.value.trim();
        currentRoom = channels[0].name;
        currentRoomType = channels[0].type;

        if (!currentUsername || !currentGuild) {
            alert('Benutzername und Server-Name sind erforderlich.');
            return;
        }

        console.log('Joining with:', { currentUsername, currentGuild, currentRoom, currentRoomType });

        // Set user info
        userAvatar.textContent = currentUsername.charAt(0).toUpperCase();
        userName.textContent = currentUsername;

        // Update header
        currentChannelName.textContent = currentRoom;
        messageInput.placeholder = `Nachricht in #${currentRoom}`;

        joinScreen.classList.add('hidden');
        appContainer.classList.remove('hidden');

        // Show text chat by default
        chatArea.classList.remove('hidden');
        voiceArea.classList.add('hidden');

        renderChannels();
        connect();
    };

    messageInput.onkeydown = (event) => {
        if (event.key === 'Enter' && messageInput.value.trim() !== '') {
            sendMessage({
                type: 'new_message',
                content: messageInput.value
            });
            
            messageInput.value = '';
        }
    };

    joinVoiceBtn.onclick = joinVoice;
    leaveVoiceBtn.onclick = leaveVoice;
    muteBtn.onclick = toggleMute;
    videoBtn.onclick = toggleVideo;
    screenShareBtn.onclick = toggleScreenShare;

</script>

</body>
</html>
