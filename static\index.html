<!-- static/index.html -->
<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <title>Discord Clone</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'gg sans', 'Noto Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background: #313338;
            color: #dbdee1;
            height: 100vh;
            overflow: hidden;
        }

        /* Join Screen */
        #join-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #5865f2 0%, #3ba55c 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        #join-box {
            background: #313338;
            padding: 32px;
            border-radius: 8px;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.24);
            text-align: center;
            min-width: 400px;
        }

        #join-box h2 {
            color: #f2f3f5;
            margin-bottom: 8px;
            font-size: 24px;
            font-weight: 600;
        }

        #join-box p {
            color: #b5bac1;
            margin-bottom: 20px;
            font-size: 16px;
        }

        #join-box input {
            display: block;
            margin: 16px auto;
            padding: 10px 12px;
            width: 100%;
            border: none;
            background: #1e1f22;
            color: #dbdee1;
            border-radius: 3px;
            font-size: 16px;
            outline: none;
            transition: background-color 0.15s ease;
        }

        #join-box input:focus {
            background: #2b2d31;
        }

        #join-box button {
            padding: 12px 24px;
            background: #5865f2;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            width: 100%;
            margin-top: 20px;
            transition: background-color 0.17s ease;
        }

        #join-box button:hover {
            background: #4752c4;
        }

        .hidden {
            display: none !important;
        }

        /* Main App Layout */
        #app-container {
            display: flex;
            width: 100%;
            height: 100vh;
        }

        /* Guild Sidebar */
        #guilds-bar {
            width: 72px;
            background: #1e1f22;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 12px 0;
            gap: 8px;
        }

        .guild-icon {
            width: 48px;
            height: 48px;
            background: #313338;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.15s ease;
            color: #80848e;
            font-weight: 600;
            font-size: 18px;
        }

        .guild-icon:hover {
            border-radius: 16px;
            background: #5865f2;
            color: #fff;
        }

        .guild-icon.active {
            border-radius: 16px;
            background: #5865f2;
            color: #fff;
        }

        .guild-separator {
            width: 32px;
            height: 2px;
            background: #35373c;
            border-radius: 1px;
            margin: 4px 0;
        }

        /* Channels Sidebar */
        #channels-bar {
            width: 240px;
            background: #2b2d31;
            display: flex;
            flex-direction: column;
        }

        #channels-header {
            padding: 12px 16px;
            border-bottom: 1px solid #1e1f22;
            font-weight: 600;
            font-size: 16px;
            color: #f2f3f5;
            background: #2b2d31;
            box-shadow: 0 1px 0 rgba(4, 4, 5, 0.2), 0 1.5px 0 rgba(6, 6, 7, 0.05), 0 2px 0 rgba(4, 4, 5, 0.05);
        }

        #channels-list {
            list-style: none;
            padding: 16px 8px;
            flex: 1;
            overflow-y: auto;
        }

        .channel-category {
            color: #949ba4;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.02em;
            margin: 16px 8px 0 8px;
            padding: 0 8px;
        }

        .channel-category:first-child {
            margin-top: 0;
        }

        #channels-list li {
            padding: 1px 8px;
            margin: 1px 0;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            color: #949ba4;
            font-weight: 500;
            transition: all 0.15s ease;
        }

        #channels-list li:hover {
            background: #35373c;
            color: #dbdee1;
        }

        #channels-list li.active {
            background: #404249;
            color: #f2f3f5;
        }

        #channels-list li .channel-icon {
            margin-right: 6px;
            font-size: 20px;
            width: 20px;
            text-align: center;
        }

        #channels-list li.text .channel-icon::before {
            content: "#";
            color: #80848e;
        }

        #channels-list li.voice .channel-icon::before {
            content: "🔊";
        }

        #channels-list li.active .channel-icon::before {
            color: #f2f3f5;
        }

        /* User Area at bottom of channels */
        #user-area {
            background: #232428;
            border-top: 1px solid #1e1f22;
        }

        /* Voice Connection Info */
        #voice-connection {
            padding: 8px;
            background: #1e1f22;
            border-bottom: 1px solid #35373c;
            display: none;
        }

        #voice-connection.connected {
            display: block;
        }

        #voice-channel-info {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }

        #voice-signal {
            width: 16px;
            height: 16px;
            margin-right: 8px;
            color: #3ba55c;
        }

        #voice-channel-name {
            font-size: 12px;
            color: #f2f3f5;
            font-weight: 600;
        }

        #voice-controls-bottom {
            display: flex;
            gap: 4px;
            justify-content: space-between;
        }

        .voice-control-btn {
            width: 32px;
            height: 32px;
            border: none;
            background: #35373c;
            color: #b5bac1;
            cursor: pointer;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.15s ease;
            font-size: 16px;
        }

        .voice-control-btn:hover {
            background: #4e5058;
            color: #dbdee1;
        }

        .voice-control-btn.active {
            background: #248046;
            color: #fff;
        }

        .voice-control-btn.muted {
            background: #da373c;
            color: #fff;
        }

        .voice-control-btn.disconnect {
            background: #da373c;
            color: #fff;
        }

        .voice-control-btn.disconnect:hover {
            background: #a12828;
        }

        /* Regular User Area */
        #user-info-area {
            padding: 8px;
            display: flex;
            align-items: center;
        }

        #user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #5865f2;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            margin-right: 8px;
        }

        #user-info {
            flex: 1;
        }

        #user-name {
            font-size: 14px;
            font-weight: 600;
            color: #f2f3f5;
        }

        #user-status {
            font-size: 12px;
            color: #949ba4;
        }

        #user-controls {
            display: flex;
            gap: 4px;
        }

        .user-control-btn {
            width: 32px;
            height: 32px;
            border: none;
            background: none;
            color: #b5bac1;
            cursor: pointer;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.15s ease;
        }

        .user-control-btn:hover {
            background: #35373c;
            color: #dbdee1;
        }

        /* Main Content Area */
        #main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: #313338;
        }

        /* Chat Header */
        #chat-header {
            height: 48px;
            background: #313338;
            border-bottom: 1px solid #1e1f22;
            display: flex;
            align-items: center;
            padding: 0 16px;
            box-shadow: 0 1px 0 rgba(4, 4, 5, 0.2), 0 1.5px 0 rgba(6, 6, 7, 0.05), 0 2px 0 rgba(4, 4, 5, 0.05);
        }

        #chat-header .channel-icon {
            color: #80848e;
            margin-right: 8px;
            font-size: 24px;
        }

        #chat-header .channel-name {
            font-size: 16px;
            font-weight: 600;
            color: #f2f3f5;
        }

        #chat-header .channel-topic {
            margin-left: 8px;
            color: #949ba4;
            font-size: 14px;
        }

        /* Chat Area */
        #chat-area {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        #messages {
            flex: 1;
            padding: 16px 0;
            overflow-y: auto;
            background: #313338;
        }

        .message {
            padding: 2px 16px;
            margin-bottom: 4px;
            position: relative;
            display: flex;
            align-items: flex-start;
            min-height: 44px;
        }

        .message:hover {
            background: #2e3035;
        }

        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #5865f2;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            margin-right: 16px;
            margin-top: 2px;
            flex-shrink: 0;
        }

        .message-content {
            flex: 1;
            min-width: 0;
        }

        .message-header {
            display: flex;
            align-items: baseline;
            margin-bottom: 2px;
        }

        .message-author {
            font-weight: 600;
            color: #f2f3f5;
            margin-right: 8px;
            cursor: pointer;
        }

        .message-timestamp {
            font-size: 12px;
            color: #949ba4;
            margin-left: 8px;
        }

        .message-text {
            color: #dbdee1;
            line-height: 1.375;
            word-wrap: break-word;
        }

        /* Message Input */
        #message-input-wrapper {
            padding: 0 16px 24px 16px;
        }

        #message-input {
            width: 100%;
            background: #383a40;
            border: none;
            padding: 11px 16px;
            border-radius: 8px;
            color: #dbdee1;
            font-size: 16px;
            outline: none;
            resize: none;
            max-height: 144px;
        }

        #message-input::placeholder {
            color: #6d6f78;
        }

        /* Voice Area */
        #voice-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 16px;
            background: #313338;
        }

        #voice-controls {
            display: flex;
            gap: 8px;
            margin-bottom: 16px;
            padding: 16px;
            background: #2b2d31;
            border-radius: 8px;
        }

        #voice-controls button {
            padding: 8px 16px;
            background: #4e5058;
            color: #dbdee1;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.15s ease;
        }

        #voice-controls button:hover {
            background: #5c5e66;
        }

        #voice-controls button.active {
            background: #248046;
            color: #fff;
        }

        #voice-controls button.muted {
            background: #da373c;
            color: #fff;
        }

        #video-grid {
            display: grid;
            gap: 16px;
            flex: 1;
            grid-template-columns: 1fr;
            grid-template-rows: 1fr;
        }

        #video-grid.two-users {
            grid-template-columns: 1fr 1fr;
        }

        #video-grid.three-users {
            grid-template-columns: 1fr 1fr;
            grid-template-rows: 1fr 1fr;
        }

        #video-grid.four-users {
            grid-template-columns: 1fr 1fr;
            grid-template-rows: 1fr 1fr;
        }

        #video-grid.many-users {
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        }

        .video-container {
            background: #1e1f22;
            border-radius: 8px;
            overflow: hidden;
            position: relative;
            min-height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
            border: 2px solid transparent;
        }

        .video-container:hover {
            border-color: #5865f2;
            transform: scale(1.02);
        }

        .video-container.fullscreen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            z-index: 1000;
            border-radius: 0;
            min-height: 100vh;
        }

        .video-container video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .video-container .username {
            position: absolute;
            bottom: 8px;
            left: 8px;
            background: rgba(0, 0, 0, 0.8);
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 600;
            color: #f2f3f5;
            z-index: 10;
        }

        .video-container .audio-only {
            font-size: 64px;
            color: #80848e;
        }

        .video-container .fullscreen-btn {
            position: absolute;
            top: 8px;
            right: 8px;
            background: rgba(0, 0, 0, 0.8);
            border: none;
            color: #f2f3f5;
            padding: 8px;
            border-radius: 4px;
            cursor: pointer;
            opacity: 0;
            transition: opacity 0.2s ease;
            z-index: 10;
        }

        .video-container:hover .fullscreen-btn {
            opacity: 1;
        }

        .video-container.fullscreen .fullscreen-btn {
            opacity: 1;
        }

        /* Members Sidebar */
        #members-bar {
            width: 240px;
            background: #2b2d31;
            display: flex;
            flex-direction: column;
            border-left: 1px solid #1e1f22;
        }

        #members-list {
            padding: 16px 8px;
            flex: 1;
            overflow-y: auto;
        }

        .member-category {
            color: #949ba4;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.02em;
            margin: 16px 8px 8px 8px;
            padding: 0 8px;
        }

        .member-category:first-child {
            margin-top: 0;
        }

        .member-category.hidden {
            display: none;
        }

        .member {
            display: flex;
            align-items: center;
            padding: 1px 8px;
            margin: 1px 0;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.15s ease;
        }

        .member:hover {
            background: #35373c;
        }

        .member-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #5865f2;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            margin-right: 12px;
            font-size: 14px;
        }

        .member-info {
            flex: 1;
            min-width: 0;
        }

        .member-name {
            font-size: 16px;
            font-weight: 500;
            color: #949ba4;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .member.online .member-name {
            color: #f2f3f5;
        }

        .member-status {
            display: flex;
            align-items: center;
            gap: 4px;
            margin-left: auto;
        }

        .voice-indicator {
            font-size: 16px;
        }

        /* Channel Management */
        .channel-actions {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 8px;
        }

        .add-channel-btn {
            background: none;
            border: none;
            color: #949ba4;
            cursor: pointer;
            font-size: 18px;
            padding: 4px;
            border-radius: 4px;
            transition: all 0.15s ease;
        }

        .add-channel-btn:hover {
            background: #35373c;
            color: #dbdee1;
        }

        .channel-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .channel-name {
            flex: 1;
        }

        .delete-channel-btn {
            background: none;
            border: none;
            color: #949ba4;
            cursor: pointer;
            font-size: 14px;
            padding: 2px 4px;
            border-radius: 3px;
            opacity: 0;
            transition: all 0.15s ease;
        }

        .channel-item:hover .delete-channel-btn {
            opacity: 1;
        }

        .delete-channel-btn:hover {
            background: #da373c;
            color: #fff;
        }

        /* Modal for creating channels */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.85);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .modal-content {
            background: #313338;
            padding: 24px;
            border-radius: 8px;
            min-width: 400px;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.24);
        }

        .modal h3 {
            color: #f2f3f5;
            margin-bottom: 16px;
            font-size: 20px;
        }

        .modal label {
            display: block;
            color: #b5bac1;
            margin-bottom: 8px;
            font-size: 14px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .modal input, .modal select {
            width: 100%;
            background: #1e1f22;
            border: none;
            padding: 10px 12px;
            border-radius: 3px;
            color: #dbdee1;
            font-size: 16px;
            margin-bottom: 16px;
            outline: none;
        }

        .modal input:focus, .modal select:focus {
            background: #2b2d31;
        }

        .modal-buttons {
            display: flex;
            gap: 12px;
            justify-content: flex-end;
        }

        .modal button {
            padding: 10px 24px;
            border: none;
            border-radius: 3px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.15s ease;
        }

        .modal .btn-cancel {
            background: transparent;
            color: #dbdee1;
        }

        .modal .btn-cancel:hover {
            background: #4e5058;
        }

        .modal .btn-create {
            background: #5865f2;
            color: #fff;
        }

        .modal .btn-create:hover {
            background: #4752c4;
        }
    </style>
</head>
<body>

<div id="join-screen">
    <div id="join-box">
        <h2>Willkommen bei Discord!</h2>
        <p>Tritt deinem Server bei</p>
        <input type="text" id="username-input" placeholder="Benutzername eingeben">
        <input type="text" id="guild-name-input" placeholder="Server-Name (z.B. 'Gaming')" value="Gaming">
        <button id="join-button">Server beitreten</button>
    </div>
</div>

<div id="app-container" class="hidden">
    <!-- Guild Sidebar -->
    <div id="guilds-bar">
        <div class="guild-icon active">G</div>
        <div class="guild-separator"></div>
        <div class="guild-icon">+</div>
    </div>

    <!-- Channels Sidebar -->
    <div id="channels-bar">
        <div id="channels-header">Gaming Server</div>
        <ul id="channels-list"></ul>

        <!-- Voice Connection Area -->
        <div id="voice-connection">
            <div id="voice-channel-info">
                <span id="voice-signal">📶</span>
                <span id="voice-channel-name">General Voice</span>
            </div>
            <div id="voice-controls-bottom">
                <button class="voice-control-btn" id="bottom-mute-btn" title="Mikrofon stumm schalten">🎤</button>
                <button class="voice-control-btn" id="bottom-headphone-btn" title="Kopfhörer stumm schalten">🎧</button>
                <button class="voice-control-btn" id="bottom-screen-btn" title="Bildschirm teilen">🖥️</button>
                <button class="voice-control-btn" id="bottom-video-btn" title="Kamera">📹</button>
                <button class="voice-control-btn disconnect" id="bottom-disconnect-btn" title="Voice verlassen">📞</button>
            </div>
        </div>

        <!-- User Area -->
        <div id="user-area">
            <div id="user-info-area">
                <div id="user-avatar"></div>
                <div id="user-info">
                    <div id="user-name"></div>
                    <div id="user-status">Online</div>
                </div>
                <div id="user-controls">
                    <button class="user-control-btn" title="Mikrofon stumm schalten">🎤</button>
                    <button class="user-control-btn" title="Kopfhörer stumm schalten">🎧</button>
                    <button class="user-control-btn" title="Einstellungen">⚙️</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Create Channel Modal -->
    <div id="create-channel-modal" class="modal hidden">
        <div class="modal-content">
            <h3>Kanal erstellen</h3>
            <label for="channel-name-input">Kanal-Name</label>
            <input type="text" id="channel-name-input" placeholder="neuer-kanal">
            <label for="channel-type-select">Kanal-Typ</label>
            <select id="channel-type-select">
                <option value="text">📝 Text-Kanal</option>
                <option value="voice">🔊 Voice-Kanal</option>
            </select>
            <div class="modal-buttons">
                <button class="btn-cancel" id="cancel-channel-btn">Abbrechen</button>
                <button class="btn-create" id="create-channel-btn">Erstellen</button>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div id="main-content">
        <!-- Chat Header -->
        <div id="chat-header">
            <span class="channel-icon">#</span>
            <span class="channel-name" id="current-channel-name">general</span>
            <span class="channel-topic">| Allgemeiner Chat für alle Themen</span>
        </div>

        <!-- Chat Area -->
        <div id="chat-area">
            <div id="messages"></div>
            <div id="message-input-wrapper">
                <input type="text" id="message-input" placeholder="Nachricht in #general">
            </div>
        </div>

        <!-- Voice Area -->
        <div id="voice-area" class="hidden">
            <div id="voice-controls">
                <button id="join-voice-btn">Voice-Kanal beitreten</button>
                <button id="mute-btn">Mikrofon</button>
                <button id="video-btn">Kamera</button>
                <button id="screen-share-btn">Bildschirm teilen</button>
                <button id="leave-voice-btn" class="hidden">Voice verlassen</button>
            </div>
            <div id="video-grid"></div>
        </div>
    </div>

    <!-- Members Sidebar -->
    <div id="members-bar">
        <div id="members-list">
            <div class="member-category">Online — <span id="online-count">0</span></div>
            <div id="online-members"></div>
            <div class="member-category hidden">Offline — <span id="offline-count">0</span></div>
            <div id="offline-members"></div>
        </div>
    </div>
</div>

<script>
    // UI Elements
    const joinScreen = document.getElementById('join-screen');
    const appContainer = document.getElementById('app-container');
    const joinButton = document.getElementById('join-button');
    const usernameInput = document.getElementById('username-input');
    const guildNameInput = document.getElementById('guild-name-input');
    const channelsList = document.getElementById('channels-list');
    const messagesDiv = document.getElementById('messages');
    const messageInput = document.getElementById('message-input');
    const chatArea = document.getElementById('chat-area');
    const voiceArea = document.getElementById('voice-area');
    const videoGrid = document.getElementById('video-grid');
    const currentChannelName = document.getElementById('current-channel-name');
    const chatHeader = document.getElementById('chat-header');
    const onlineMembers = document.getElementById('online-members');
    const offlineMembers = document.getElementById('offline-members');
    const onlineCount = document.getElementById('online-count');
    const offlineCount = document.getElementById('offline-count');
    const userAvatar = document.getElementById('user-avatar');
    const userName = document.getElementById('user-name');

    // Voice connection elements
    const voiceConnection = document.getElementById('voice-connection');
    const voiceChannelName = document.getElementById('voice-channel-name');
    const bottomMuteBtn = document.getElementById('bottom-mute-btn');
    const bottomHeadphoneBtn = document.getElementById('bottom-headphone-btn');
    const bottomScreenBtn = document.getElementById('bottom-screen-btn');
    const bottomVideoBtn = document.getElementById('bottom-video-btn');
    const bottomDisconnectBtn = document.getElementById('bottom-disconnect-btn');

    // Voice controls
    const joinVoiceBtn = document.getElementById('join-voice-btn');
    const leaveVoiceBtn = document.getElementById('leave-voice-btn');
    const muteBtn = document.getElementById('mute-btn');
    const videoBtn = document.getElementById('video-btn');
    const screenShareBtn = document.getElementById('screen-share-btn');

    // Channel management
    const createChannelModal = document.getElementById('create-channel-modal');
    const channelNameInput = document.getElementById('channel-name-input');
    const channelTypeSelect = document.getElementById('channel-type-select');
    const createChannelBtn = document.getElementById('create-channel-btn');
    const cancelChannelBtn = document.getElementById('cancel-channel-btn');

    let ws;
    let currentGuild, currentRoom, currentUsername, currentRoomType;
    let currentVoiceChannel = null; // Track which voice channel we're connected to
    let localStream;
    let peerConnections = new Map();
    let isInVoice = false;
    let isMuted = false;
    let isVideoEnabled = false;
    let isScreenSharing = false;
    let fullscreenVideo = null;

    let channels = [
        { name: 'general', type: 'text', category: 'Text-Kanäle' },
        { name: 'random', type: 'text', category: 'Text-Kanäle' },
        { name: 'General Voice', type: 'voice', category: 'Voice-Kanäle' },
        { name: 'Gaming', type: 'voice', category: 'Voice-Kanäle' }
    ];

    const rtcConfiguration = {
        iceServers: [
            { urls: 'stun:stun.l.google.com:19302' },
            { urls: 'stun:stun1.l.google.com:19302' }
        ]
    };

    function connect() {
        if (ws) {
            ws.close();
        }

        const wsUrl = `ws://${window.location.host}/ws?guild=${encodeURIComponent(currentGuild)}&room=${encodeURIComponent(currentRoom)}&type=${currentRoomType}&username=${encodeURIComponent(currentUsername)}`;
        console.log('Connecting to:', wsUrl);
        
        ws = new WebSocket(wsUrl);

        ws.onopen = () => {
            console.log(`Verbunden mit ${currentGuild}/${currentRoom} (${currentRoomType})`);
            if (currentRoomType === 'text') {
                messagesDiv.innerHTML = '';
                console.log('Connected to text channel, cleared messages');
            }
        };

        ws.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);
                handleMessage(data);
            } catch (error) {
                console.error('Error parsing message:', error);
            }
        };

        ws.onclose = () => {
            console.log('Verbindung getrennt.');
        };
        
        ws.onerror = (error) => {
            console.error('WebSocket-Fehler:', error);
        };
    }

    function handleMessage(data) {
        console.log('Received message:', data);
        
        switch (data.type) {
            case 'history':
                if (data.data) {
                    data.data.forEach(msg => displayMessage(msg));
                }
                break;
            case 'new_message':
                displayMessage(data);
                break;
            case 'user_list':
                updateUserList(data.data);
                break;
            case 'voice_users':
                updateVoiceUserList(data.data);
                break;
            case 'webrtc_initiate':
                handleWebRTCInitiate(data);
                break;
            case 'webrtc_offer':
                handleWebRTCOffer(data);
                break;
            case 'webrtc_answer':
                handleWebRTCAnswer(data);
                break;
            case 'webrtc_ice':
                handleWebRTCICE(data);
                break;
            case 'channel_list':
                updateChannelList(data.data);
                break;
        }
    }

    function displayMessage(msg) {
        console.log('Displaying message:', msg);

        const msgElement = document.createElement('div');
        msgElement.className = 'message';

        const avatar = document.createElement('div');
        avatar.className = 'message-avatar';
        avatar.textContent = msg.author.charAt(0).toUpperCase();

        const content = document.createElement('div');
        content.className = 'message-content';

        const header = document.createElement('div');
        header.className = 'message-header';

        const author = document.createElement('span');
        author.className = 'message-author';
        author.textContent = msg.author;

        const timestamp = document.createElement('span');
        timestamp.className = 'message-timestamp';
        timestamp.textContent = new Date().toLocaleTimeString('de-DE', {
            hour: '2-digit',
            minute: '2-digit'
        });

        const text = document.createElement('div');
        text.className = 'message-text';
        text.textContent = msg.content;

        header.appendChild(author);
        header.appendChild(timestamp);
        content.appendChild(header);
        content.appendChild(text);
        msgElement.appendChild(avatar);
        msgElement.appendChild(content);

        messagesDiv.appendChild(msgElement);
        messagesDiv.scrollTop = messagesDiv.scrollHeight;
    }

    function updateUserList(users) {
        onlineMembers.innerHTML = '';
        if (users) {
            onlineCount.textContent = users.length;
            console.log('Updating user list:', users);

            users.forEach(user => {
                const memberElement = document.createElement('div');
                memberElement.className = 'member online';

                const avatar = document.createElement('div');
                avatar.className = 'member-avatar';
                avatar.textContent = user.charAt(0).toUpperCase();

                const info = document.createElement('div');
                info.className = 'member-info';

                const name = document.createElement('div');
                name.className = 'member-name';
                name.textContent = user;

                info.appendChild(name);
                memberElement.appendChild(avatar);
                memberElement.appendChild(info);

                if (user === currentUsername) {
                    name.style.fontWeight = '600';
                    name.style.color = '#f2f3f5';
                }

                onlineMembers.appendChild(memberElement);
            });
        } else {
            onlineCount.textContent = '0';
        }
    }

    function updateVoiceUserList(voiceUsers) {
        // Clear existing video elements first
        videoGrid.innerHTML = '';

        // Update the online members list to show voice status
        if (voiceUsers && voiceUsers.length > 0) {
            // Add voice users to video grid
            voiceUsers.forEach(user => {
                // Add video element for each voice user
                if (user.username === currentUsername) {
                    // Add our own video element
                    addVideoElement(user.username, localStream, true);
                } else {
                    // Add placeholder for remote users (will be updated when WebRTC connects)
                    addVideoElement(user.username, null, false);
                }
            });

            updateVideoGridLayout();

            // Update online members to show voice indicators
            updateOnlineMembersWithVoiceStatus(voiceUsers);
        }
    }

    function updateOnlineMembersWithVoiceStatus(voiceUsers) {
        const voiceUsernames = voiceUsers.map(user => user.username);

        // Update existing online members with voice indicators
        const memberElements = onlineMembers.querySelectorAll('.member');
        memberElements.forEach(memberElement => {
            const nameElement = memberElement.querySelector('.member-name');
            const username = nameElement.textContent;

            // Remove existing voice indicators
            let statusElement = memberElement.querySelector('.member-status');
            if (!statusElement) {
                statusElement = document.createElement('div');
                statusElement.className = 'member-status';
                memberElement.appendChild(statusElement);
            }
            statusElement.innerHTML = '';

            // Add voice indicators if user is in voice
            const voiceUser = voiceUsers.find(vu => vu.username === username);
            if (voiceUser) {
                memberElement.style.backgroundColor = '#3ba55c20'; // Slight green background

                if (voiceUser.audio_muted) {
                    const muteIcon = document.createElement('span');
                    muteIcon.className = 'voice-indicator';
                    muteIcon.textContent = '🔇';
                    statusElement.appendChild(muteIcon);
                } else {
                    const voiceIcon = document.createElement('span');
                    voiceIcon.className = 'voice-indicator';
                    voiceIcon.textContent = '🎤';
                    statusElement.appendChild(voiceIcon);
                }

                if (voiceUser.video_enabled) {
                    const videoIcon = document.createElement('span');
                    videoIcon.className = 'voice-indicator';
                    videoIcon.textContent = '📹';
                    statusElement.appendChild(videoIcon);
                }

                if (voiceUser.screen_share) {
                    const screenIcon = document.createElement('span');
                    screenIcon.className = 'voice-indicator';
                    screenIcon.textContent = '🖥️';
                    statusElement.appendChild(screenIcon);
                }
            } else {
                memberElement.style.backgroundColor = ''; // Remove background
            }
        });
    }

    function renderChannels() {
        channelsList.innerHTML = '';

        // Group channels by category
        const categories = {};
        channels.forEach(channel => {
            if (!categories[channel.category]) {
                categories[channel.category] = [];
            }
            categories[channel.category].push(channel);
        });

        // Render each category
        Object.keys(categories).forEach(categoryName => {
            const categoryHeader = document.createElement('div');
            categoryHeader.className = 'channel-actions';

            const categoryElement = document.createElement('div');
            categoryElement.className = 'channel-category';
            categoryElement.textContent = categoryName;

            const addBtn = document.createElement('button');
            addBtn.className = 'add-channel-btn';
            addBtn.textContent = '+';
            addBtn.title = 'Kanal erstellen';
            addBtn.onclick = () => showCreateChannelModal(categoryName);

            categoryHeader.appendChild(categoryElement);
            categoryHeader.appendChild(addBtn);
            channelsList.appendChild(categoryHeader);

            categories[categoryName].forEach(channel => {
                const li = document.createElement('li');
                li.className = channel.type;
                li.dataset.channel = channel.name;
                li.dataset.type = channel.type;

                const channelItem = document.createElement('div');
                channelItem.className = 'channel-item';

                const channelContent = document.createElement('div');
                channelContent.style.display = 'flex';
                channelContent.style.alignItems = 'center';
                channelContent.style.flex = '1';

                const icon = document.createElement('span');
                icon.className = 'channel-icon';

                const name = document.createElement('span');
                name.className = 'channel-name';
                name.textContent = channel.name;

                const deleteBtn = document.createElement('button');
                deleteBtn.className = 'delete-channel-btn';
                deleteBtn.textContent = '×';
                deleteBtn.title = 'Kanal löschen';
                deleteBtn.onclick = (e) => {
                    e.stopPropagation();
                    deleteChannel(channel.name);
                };

                channelContent.appendChild(icon);
                channelContent.appendChild(name);
                channelItem.appendChild(channelContent);

                // Don't show delete button for default channels
                if (!['general', 'General Voice'].includes(channel.name)) {
                    channelItem.appendChild(deleteBtn);
                }

                li.appendChild(channelItem);

                if (channel.name === currentRoom) {
                    li.classList.add('active');
                }

                li.onclick = () => switchChannel(channel.name, channel.type);
                channelsList.appendChild(li);
            });
        });
    }

    function switchChannel(channelName, channelType) {
        if (currentRoom === channelName) return;

        currentRoom = channelName;
        currentRoomType = channelType;

        // Update channel name in header
        currentChannelName.textContent = channelName;

        // Update header icon and placeholder
        const headerIcon = chatHeader.querySelector('.channel-icon');
        if (channelType === 'text') {
            headerIcon.textContent = '#';
            messageInput.placeholder = `Nachricht in #${channelName}`;
            // Always show text chat when clicking on text channel
            chatArea.classList.remove('hidden');
            voiceArea.classList.add('hidden');
        } else {
            headerIcon.textContent = '🔊';
            // For voice channels, always show voice area
            chatArea.classList.add('hidden');
            voiceArea.classList.remove('hidden');
        }

        renderChannels();
        connect();
    }

    async function joinVoice() {
        if (currentRoomType !== 'voice') {
            alert('Sie können nur Voice-Kanälen beitreten.');
            return;
        }

        try {
            // Try to get audio, but don't require it
            let constraints = { audio: true, video: false };

            try {
                localStream = await navigator.mediaDevices.getUserMedia(constraints);
            } catch (audioError) {
                console.log('Audio not available, joining voice without microphone');
                // Create a silent audio track
                localStream = new MediaStream();
            }

            isInVoice = true;
            currentVoiceChannel = currentRoom;

            // Update UI
            joinVoiceBtn.classList.add('hidden');
            leaveVoiceBtn.classList.remove('hidden');

            // Show voice connection area
            voiceConnection.classList.add('connected');
            voiceChannelName.textContent = currentVoiceChannel;

            // Stay in voice area - don't switch to text
            chatArea.classList.add('hidden');
            voiceArea.classList.remove('hidden');

            // Add our own video element immediately
            addVideoElement(currentUsername, localStream, true);

            sendMessage({
                type: 'join_voice'
            });

        } catch (error) {
            console.error('Fehler beim Beitreten des Voice-Channels:', error);
            // Still allow joining without microphone
            isInVoice = true;
            currentVoiceChannel = currentRoom;

            joinVoiceBtn.classList.add('hidden');
            leaveVoiceBtn.classList.remove('hidden');

            // Show voice connection area
            voiceConnection.classList.add('connected');
            voiceChannelName.textContent = currentVoiceChannel;

            // Stay in voice area - don't switch to text
            chatArea.classList.add('hidden');
            voiceArea.classList.remove('hidden');

            // Add our own video element immediately
            addVideoElement(currentUsername, localStream, true);

            sendMessage({
                type: 'join_voice'
            });
        }
    }

    function leaveVoice() {
        if (localStream) {
            localStream.getTracks().forEach(track => track.stop());
            localStream = null;
        }

        // Close all peer connections
        peerConnections.forEach(pc => pc.close());
        peerConnections.clear();

        // Clear video grid
        videoGrid.innerHTML = '';

        isInVoice = false;
        currentVoiceChannel = null;
        isVideoEnabled = false;
        isScreenSharing = false;

        // Update UI
        joinVoiceBtn.classList.remove('hidden');
        leaveVoiceBtn.classList.add('hidden');
        videoBtn.classList.remove('active');
        screenShareBtn.classList.remove('active');

        // Hide voice connection area
        voiceConnection.classList.remove('connected');

        // Reset button states
        bottomMuteBtn.classList.remove('muted');
        bottomVideoBtn.classList.remove('active');
        bottomScreenBtn.classList.remove('active');

        sendMessage({
            type: 'leave_voice'
        });
    }

    async function toggleVideo() {
        if (!isInVoice) return;
        
        try {
            if (!isVideoEnabled) {
                const videoStream = await navigator.mediaDevices.getUserMedia({ 
                    video: true, 
                    audio: true 
                });
                
                localStream = videoStream;
                isVideoEnabled = true;
                videoBtn.classList.add('active');
                bottomVideoBtn.classList.add('active');
                
                updateVideoElement(currentUsername, localStream);
                
            } else {
                localStream.getVideoTracks().forEach(track => track.stop());
                try {
                    localStream = await navigator.mediaDevices.getUserMedia({ audio: true });
                } catch (e) {
                    localStream = new MediaStream();
                }
                
                isVideoEnabled = false;
                videoBtn.classList.remove('active');
                bottomVideoBtn.classList.remove('active');
                
                updateVideoElement(currentUsername, localStream);
            }
            
            updatePeerConnections();
            
            sendMessage({
                type: 'video_toggle',
                data: isVideoEnabled
            });
            
        } catch (error) {
            console.error('Fehler beim Video-Toggle:', error);
        }
    }

    async function toggleScreenShare() {
        if (!isInVoice) return;

        try {
            if (!isScreenSharing) {
                console.log('Starting screen share...');
                const screenStream = await navigator.mediaDevices.getDisplayMedia({
                    video: true,
                    audio: true
                });

                // Stop existing tracks
                if (localStream) {
                    localStream.getTracks().forEach(track => track.stop());
                }

                localStream = screenStream;
                isScreenSharing = true;
                screenShareBtn.classList.add('active');
                bottomScreenBtn.classList.add('active');

                // Update our own video element immediately
                updateVideoElement(currentUsername, localStream);

                screenStream.getVideoTracks()[0].onended = () => {
                    console.log('Screen share ended by user');
                    toggleScreenShare();
                };

            } else {
                console.log('Stopping screen share...');
                localStream.getTracks().forEach(track => track.stop());
                try {
                    localStream = await navigator.mediaDevices.getUserMedia({
                        audio: true,
                        video: isVideoEnabled
                    });
                } catch (e) {
                    console.log('Could not get user media, creating empty stream');
                    localStream = new MediaStream();
                }

                isScreenSharing = false;
                screenShareBtn.classList.remove('active');
                bottomScreenBtn.classList.remove('active');

                // Update our own video element immediately
                updateVideoElement(currentUsername, localStream);
            }

            updatePeerConnections();

            sendMessage({
                type: 'screen_share',
                data: isScreenSharing
            });

        } catch (error) {
            console.error('Fehler beim Screen-Share:', error);
        }
    }

    function toggleMute() {
        if (!localStream) return;

        const audioTrack = localStream.getAudioTracks()[0];
        if (audioTrack) {
            audioTrack.enabled = !audioTrack.enabled;
            isMuted = !audioTrack.enabled;

            // Update both buttons
            if (isMuted) {
                muteBtn.classList.add('muted');
                muteBtn.textContent = 'Mikrofon (Stumm)';
                bottomMuteBtn.classList.add('muted');
            } else {
                muteBtn.classList.remove('muted');
                muteBtn.textContent = 'Mikrofon';
                bottomMuteBtn.classList.remove('muted');
            }

            sendMessage({
                type: 'audio_toggle',
                data: isMuted
            });
        }
    }

    function addVideoElement(username, stream, isLocal = false) {
        // Remove existing element if it exists
        removeVideoElement(username);

        const container = document.createElement('div');
        container.className = 'video-container';
        container.id = `video-${username}`;

        if (stream && stream.getVideoTracks().length > 0) {
            const video = document.createElement('video');
            video.srcObject = stream;
            video.autoplay = true;
            video.muted = isLocal;
            container.appendChild(video);
        } else {
            // Audio-only user
            const audioIcon = document.createElement('div');
            audioIcon.className = 'audio-only';
            audioIcon.textContent = '🎤';
            container.appendChild(audioIcon);
        }

        const label = document.createElement('div');
        label.className = 'username';
        label.textContent = username;

        const fullscreenBtn = document.createElement('button');
        fullscreenBtn.className = 'fullscreen-btn';
        fullscreenBtn.textContent = '⛶';
        fullscreenBtn.title = 'Vollbild';
        fullscreenBtn.onclick = (e) => {
            e.stopPropagation();
            toggleFullscreen(username);
        };

        container.appendChild(label);
        container.appendChild(fullscreenBtn);

        // Add click handler for fullscreen
        container.onclick = () => toggleFullscreen(username);

        videoGrid.appendChild(container);
        updateVideoGridLayout();
    }

    function updateVideoElement(username, stream) {
        const container = document.getElementById(`video-${username}`);
        if (container) {
            console.log('Updating video element for:', username, 'with stream:', stream);

            // Clear container content but preserve structure
            const existingVideo = container.querySelector('video');
            const existingAudioIcon = container.querySelector('.audio-only');

            // Remove existing video/audio content
            if (existingVideo) existingVideo.remove();
            if (existingAudioIcon) existingAudioIcon.remove();

            if (stream && (stream.getVideoTracks().length > 0 || stream.getAudioTracks().length > 0)) {
                const video = document.createElement('video');
                video.srcObject = stream;
                video.autoplay = true;
                video.muted = username === currentUsername;
                video.playsInline = true;

                // Insert video before username label
                const label = container.querySelector('.username');
                container.insertBefore(video, label);

                console.log('Video element created for:', username);
            } else {
                const audioIcon = document.createElement('div');
                audioIcon.className = 'audio-only';
                audioIcon.textContent = '🎤';

                // Insert audio icon before username label
                const label = container.querySelector('.username');
                container.insertBefore(audioIcon, label);
            }
        }
    }

    function removeVideoElement(username) {
        const container = document.getElementById(`video-${username}`);
        if (container) {
            container.remove();
            updateVideoGridLayout();
        }
    }

    function updateVideoGridLayout() {
        const videoContainers = videoGrid.querySelectorAll('.video-container:not(.fullscreen)');
        const count = videoContainers.length;

        // Reset classes
        videoGrid.className = '';

        if (count === 1) {
            videoGrid.classList.add('one-user');
        } else if (count === 2) {
            videoGrid.classList.add('two-users');
        } else if (count === 3 || count === 4) {
            videoGrid.classList.add('four-users');
        } else if (count > 4) {
            videoGrid.classList.add('many-users');
        }
    }

    function toggleFullscreen(username) {
        const container = document.getElementById(`video-${username}`);
        if (!container) return;

        if (fullscreenVideo === username) {
            // Exit fullscreen
            container.classList.remove('fullscreen');
            fullscreenVideo = null;
            updateVideoGridLayout();
        } else {
            // Enter fullscreen
            if (fullscreenVideo) {
                const prevContainer = document.getElementById(`video-${fullscreenVideo}`);
                if (prevContainer) {
                    prevContainer.classList.remove('fullscreen');
                }
            }
            container.classList.add('fullscreen');
            fullscreenVideo = username;
        }

        // Update fullscreen button text
        const btn = container.querySelector('.fullscreen-btn');
        if (btn) {
            btn.textContent = container.classList.contains('fullscreen') ? '⛶' : '⛶';
        }
    }

    function showCreateChannelModal(category) {
        channelTypeSelect.value = category === 'Text-Kanäle' ? 'text' : 'voice';
        channelNameInput.value = '';
        createChannelModal.classList.remove('hidden');
        channelNameInput.focus();
    }

    function hideCreateChannelModal() {
        createChannelModal.classList.add('hidden');
    }

    function createChannel() {
        const name = channelNameInput.value.trim();
        const type = channelTypeSelect.value;

        if (!name) {
            alert('Bitte geben Sie einen Kanal-Namen ein.');
            return;
        }

        // Check if channel already exists
        if (channels.find(ch => ch.name === name)) {
            alert('Ein Kanal mit diesem Namen existiert bereits.');
            return;
        }

        const category = type === 'text' ? 'Text-Kanäle' : 'Voice-Kanäle';

        // Send create channel message to server
        sendMessage({
            type: 'create_channel',
            data: { name, type, category }
        });

        hideCreateChannelModal();
    }

    function deleteChannel(channelName) {
        if (['general', 'General Voice'].includes(channelName)) {
            alert('Standard-Kanäle können nicht gelöscht werden.');
            return;
        }

        if (confirm(`Möchten Sie den Kanal "${channelName}" wirklich löschen?`)) {
            // Send delete channel message to server
            sendMessage({
                type: 'delete_channel',
                data: channelName
            });
        }
    }

    function updateChannelList(serverChannels) {
        channels = serverChannels;
        renderChannels();
    }

    async function handleWebRTCInitiate(data) {
        if (isInVoice) {
            console.log('WebRTC initiate from:', data.data);
            const pc = await createPeerConnection(data.from, data.data.username);
            const offer = await pc.createOffer();
            await pc.setLocalDescription(offer);

            sendMessage({
                type: 'webrtc_offer',
                target: data.from,
                data: offer
            });
        }
    }

    async function createPeerConnection(targetUserId, targetUsername = null) {
        const pc = new RTCPeerConnection(rtcConfiguration);
        peerConnections.set(targetUserId, pc);

        if (localStream) {
            localStream.getTracks().forEach(track => {
                pc.addTrack(track, localStream);
            });
        }

        pc.ontrack = (event) => {
            const remoteStream = event.streams[0];
            console.log('Received remote stream from:', targetUserId, targetUsername);

            // Use the provided username or fallback
            const remoteUsername = targetUsername || `User-${targetUserId.slice(-4)}`;

            // Update existing video element with the stream
            const existingContainer = document.getElementById(`video-${remoteUsername}`);
            if (existingContainer) {
                updateVideoElement(remoteUsername, remoteStream);
            } else {
                addVideoElement(remoteUsername, remoteStream);
            }
        };

        pc.onicecandidate = (event) => {
            if (event.candidate) {
                sendMessage({
                    type: 'webrtc_ice',
                    target: targetUserId,
                    data: event.candidate
                });
            }
        };

        return pc;
    }

    async function handleWebRTCOffer(data) {
        const pc = await createPeerConnection(data.from);
        await pc.setRemoteDescription(data.data);

        const answer = await pc.createAnswer();
        await pc.setLocalDescription(answer);

        sendMessage({
            type: 'webrtc_answer',
            target: data.from,
            data: answer
        });
    }

    async function handleWebRTCAnswer(data) {
        const pc = peerConnections.get(data.from);
        if (pc) {
            await pc.setRemoteDescription(data.data);
        }
    }

    async function handleWebRTCICE(data) {
        const pc = peerConnections.get(data.from);
        if (pc) {
            await pc.addIceCandidate(data.data);
        }
    }

    function updatePeerConnections() {
        peerConnections.forEach(async (pc, userId) => {
            const senders = pc.getSenders();
            senders.forEach(sender => {
                if (sender.track) {
                    pc.removeTrack(sender);
                }
            });
            
            if (localStream) {
                localStream.getTracks().forEach(track => {
                    pc.addTrack(track, localStream);
                });
            }
        });
    }

    function sendMessage(message) {
        if (ws && ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify(message));
        } else {
            console.error('WebSocket not connected');
        }
    }

    // Event Listeners
    joinButton.onclick = () => {
        currentUsername = usernameInput.value.trim();
        currentGuild = guildNameInput.value.trim();
        currentRoom = channels[0].name;
        currentRoomType = channels[0].type;

        if (!currentUsername || !currentGuild) {
            alert('Benutzername und Server-Name sind erforderlich.');
            return;
        }

        console.log('Joining with:', { currentUsername, currentGuild, currentRoom, currentRoomType });

        // Set user info
        userAvatar.textContent = currentUsername.charAt(0).toUpperCase();
        userName.textContent = currentUsername;

        // Update header
        currentChannelName.textContent = currentRoom;
        messageInput.placeholder = `Nachricht in #${currentRoom}`;

        joinScreen.classList.add('hidden');
        appContainer.classList.remove('hidden');

        // Show text chat by default
        chatArea.classList.remove('hidden');
        voiceArea.classList.add('hidden');

        renderChannels();
        connect();
    };

    messageInput.onkeydown = (event) => {
        if (event.key === 'Enter' && messageInput.value.trim() !== '') {
            console.log('Sending message:', messageInput.value);
            sendMessage({
                type: 'new_message',
                content: messageInput.value.trim()
            });

            messageInput.value = '';
        }
    };

    joinVoiceBtn.onclick = joinVoice;
    leaveVoiceBtn.onclick = leaveVoice;
    muteBtn.onclick = toggleMute;
    videoBtn.onclick = toggleVideo;
    screenShareBtn.onclick = toggleScreenShare;

    // Bottom voice controls
    bottomMuteBtn.onclick = toggleMute;
    bottomVideoBtn.onclick = toggleVideo;
    bottomScreenBtn.onclick = toggleScreenShare;
    bottomDisconnectBtn.onclick = leaveVoice;

    // Channel management event listeners
    createChannelBtn.onclick = createChannel;
    cancelChannelBtn.onclick = hideCreateChannelModal;

    // Close modal when clicking outside
    createChannelModal.onclick = (e) => {
        if (e.target === createChannelModal) {
            hideCreateChannelModal();
        }
    };

    // Handle Enter key in channel name input
    channelNameInput.onkeydown = (e) => {
        if (e.key === 'Enter') {
            createChannel();
        } else if (e.key === 'Escape') {
            hideCreateChannelModal();
        }
    };

    // Handle Escape key for fullscreen
    document.onkeydown = (e) => {
        if (e.key === 'Escape' && fullscreenVideo) {
            toggleFullscreen(fullscreenVideo);
        }
    };

</script>

</body>
</html>
