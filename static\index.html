<!-- static/index.html -->
<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <title>Go Discord Clone - Voice & Video</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif; display: flex; height: 100vh; margin: 0; background: #36393f; color: #dcddde; }
        #join-screen { position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.8); display: flex; align-items: center; justify-content: center; z-index: 100; }
        #join-box { background: #2f3136; padding: 20px; border-radius: 8px; text-align: center; }
        #join-box input { display: block; margin: 10px auto; padding: 10px; width: 200px; border: 1px solid #40444b; background: #40444b; color: #dcddde; border-radius: 4px; }
        #join-box button { padding: 10px 20px; background: #5865f2; color: white; border: none; border-radius: 4px; cursor: pointer; margin-top: 10px; }
        .hidden { display: none !important; }
        #app-container { display: flex; width: 100%; height: 100%; }
        #guilds-bar { width: 72px; background: #202225; padding-top: 12px; }
        #channels-bar { width: 240px; background: #2f3136; display: flex; flex-direction: column; }
        #channels-header { padding: 18px; font-weight: bold; border-bottom: 1px solid #282b30; }
        #channels-list { padding: 10px; list-style: none; margin: 0; }
        #channels-list li { padding: 8px 12px; border-radius: 4px; cursor: pointer; display: flex; align-items: center; }
        #channels-list li.active { background: #40444b; color: #fff; }
        #channels-list li:hover { background: #3a3d42; }
        #channels-list li.text::before { content: "#"; margin-right: 8px; }
        #channels-list li.voice::before { content: "🔊"; margin-right: 8px; }
        #main-content { flex-grow: 1; display: flex; flex-direction: column; }
        #chat-area { flex-grow: 1; display: flex; flex-direction: column; }
        #voice-area { flex-grow: 1; display: flex; flex-direction: column; padding: 20px; }
        #messages { flex-grow: 1; padding: 20px; overflow-y: auto; background: #36393f; }
        .message { margin-bottom: 10px; padding: 5px; }
        .message .author { font-weight: bold; color: #ffffff; }
        .message .content { margin-left: 10px; }
        #message-input-wrapper { padding: 20px; }
        #message-input { width: 100%; background: #40444b; border: none; padding: 12px; border-radius: 8px; color: #dcddde; box-sizing: border-box; }
        #users-bar { width: 240px; background: #2f3136; padding: 10px; }
        #user-list, #voice-user-list { list-style: none; padding: 0; margin: 10px 0; }
        #user-list li, #voice-user-list li { padding: 5px; margin: 2px 0; }
        #voice-controls { display: flex; gap: 10px; margin-bottom: 20px; }
        #voice-controls button { padding: 10px 15px; background: #5865f2; color: white; border: none; border-radius: 4px; cursor: pointer; }
        #voice-controls button:hover { background: #4752c4; }
        #voice-controls button.active { background: #3ba55c; }
        #voice-controls button.muted { background: #ed4245; }
        #video-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 10px; flex-grow: 1; }
        .video-container { background: #2f3136; border-radius: 8px; overflow: hidden; position: relative; min-height: 200px; display: flex; align-items: center; justify-content: center; }
        .video-container video { width: 100%; height: 200px; object-fit: cover; }
        .video-container .username { position: absolute; bottom: 10px; left: 10px; background: rgba(0,0,0,0.7); padding: 5px 10px; border-radius: 4px; font-size: 12px; }
        .video-container .audio-only { font-size: 48px; }
        .voice-user { display: flex; align-items: center; padding: 5px; margin: 2px 0; border-radius: 4px; }
        .voice-user.speaking { background: #3ba55c; }
        .voice-user .status { margin-left: auto; font-size: 12px; }
    </style>
</head>
<body>

<div id="join-screen">
    <div id="join-box">
        <h2>Willkommen!</h2>
        <input type="text" id="username-input" placeholder="Wähle einen Benutzernamen">
        <input type="text" id="guild-name-input" placeholder="Gildenname (z.B. 'Gaming')" value="Gaming">
        <button id="join-button">Beitreten</button>
    </div>
</div>

<div id="app-container" class="hidden">
    <div id="guilds-bar"></div>
    <div id="channels-bar">
        <div id="channels-header">Kanäle</div>
        <ul id="channels-list"></ul>
    </div>
    <div id="main-content">
        <div id="chat-area">
            <div id="messages"></div>
            <div id="message-input-wrapper">
                <input type="text" id="message-input" placeholder="Nachricht senden...">
            </div>
        </div>
        <div id="voice-area" class="hidden">
            <div id="voice-controls">
                <button id="join-voice-btn">Voice beitreten</button>
                <button id="mute-btn">Stumm</button>
                <button id="video-btn">Video</button>
                <button id="screen-share-btn">Bildschirm teilen</button>
                <button id="leave-voice-btn" class="hidden">Voice verlassen</button>
            </div>
            <div id="video-grid"></div>
        </div>
    </div>
    <div id="users-bar">
        <h3>Online</h3>
        <ul id="user-list"></ul>
        <h3>Voice</h3>
        <ul id="voice-user-list"></ul>
    </div>
</div>

<script>
    // UI Elements
    const joinScreen = document.getElementById('join-screen');
    const appContainer = document.getElementById('app-container');
    const joinButton = document.getElementById('join-button');
    const usernameInput = document.getElementById('username-input');
    const guildNameInput = document.getElementById('guild-name-input');
    const channelsList = document.getElementById('channels-list');
    const messagesDiv = document.getElementById('messages');
    const messageInput = document.getElementById('message-input');
    const userList = document.getElementById('user-list');
    const voiceUserList = document.getElementById('voice-user-list');
    const chatArea = document.getElementById('chat-area');
    const voiceArea = document.getElementById('voice-area');
    const videoGrid = document.getElementById('video-grid');
    
    // Voice controls
    const joinVoiceBtn = document.getElementById('join-voice-btn');
    const leaveVoiceBtn = document.getElementById('leave-voice-btn');
    const muteBtn = document.getElementById('mute-btn');
    const videoBtn = document.getElementById('video-btn');
    const screenShareBtn = document.getElementById('screen-share-btn');

    let ws;
    let currentGuild, currentRoom, currentUsername, currentRoomType;
    let localStream;
    let peerConnections = new Map();
    let isInVoice = false;
    let isMuted = false;
    let isVideoEnabled = false;
    let isScreenSharing = false;

    const channels = [
        { name: 'general', type: 'text' },
        { name: 'random', type: 'text' },
        { name: 'ankündigungen', type: 'text' },
        { name: 'General Voice', type: 'voice' },
        { name: 'Gaming', type: 'voice' },
        { name: 'Music', type: 'voice' }
    ];

    const rtcConfiguration = {
        iceServers: [
            { urls: 'stun:stun.l.google.com:19302' },
            { urls: 'stun:stun1.l.google.com:19302' }
        ]
    };

    function connect() {
        if (ws) {
            ws.close();
        }

        const wsUrl = `ws://${window.location.host}/ws?guild=${encodeURIComponent(currentGuild)}&room=${encodeURIComponent(currentRoom)}&type=${currentRoomType}&username=${encodeURIComponent(currentUsername)}`;
        console.log('Connecting to:', wsUrl);
        
        ws = new WebSocket(wsUrl);

        ws.onopen = () => {
            console.log(`Verbunden mit ${currentGuild}/${currentRoom} (${currentRoomType})`);
            if (currentRoomType === 'text') {
                messagesDiv.innerHTML = '';
            }
        };

        ws.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);
                handleMessage(data);
            } catch (error) {
                console.error('Error parsing message:', error);
            }
        };

        ws.onclose = () => {
            console.log('Verbindung getrennt.');
        };
        
        ws.onerror = (error) => {
            console.error('WebSocket-Fehler:', error);
        };
    }

    function handleMessage(data) {
        console.log('Received message:', data);
        
        switch (data.type) {
            case 'history':
                if (data.data) {
                    data.data.forEach(msg => displayMessage(msg));
                }
                break;
            case 'new_message':
                displayMessage(data);
                break;
            case 'user_list':
                updateUserList(data.data);
                break;
            case 'voice_users':
                updateVoiceUserList(data.data);
                break;
            case 'webrtc_initiate':
                handleWebRTCInitiate(data);
                break;
            case 'webrtc_offer':
                handleWebRTCOffer(data);
                break;
            case 'webrtc_answer':
                handleWebRTCAnswer(data);
                break;
            case 'webrtc_ice':
                handleWebRTCICE(data);
                break;
        }
    }

    function displayMessage(msg) {
        const msgElement = document.createElement('div');
        msgElement.className = 'message';
        msgElement.innerHTML = `<span class="author">${msg.author}:</span><span class="content">${msg.content}</span>`;
        messagesDiv.appendChild(msgElement);
        messagesDiv.scrollTop = messagesDiv.scrollHeight;
    }

    function updateUserList(users) {
        userList.innerHTML = '';
        if (users) {
            users.forEach(user => {
                const userElement = document.createElement('li');
                userElement.textContent = user;
                if (user === currentUsername) {
                    userElement.style.fontWeight = 'bold';
                }
                userList.appendChild(userElement);
            });
        }
    }

    function updateVoiceUserList(voiceUsers) {
        voiceUserList.innerHTML = '';
        if (voiceUsers) {
            voiceUsers.forEach(user => {
                const userElement = document.createElement('li');
                userElement.className = 'voice-user';
                userElement.innerHTML = `
                    <span>${user.username}</span>
                    <span class="status">
                        ${user.audio_muted ? '🔇' : '🔊'}
                        ${user.video_enabled ? '📹' : ''}
                        ${user.screen_share ? '🖥️' : ''}
                    </span>
                `;
                voiceUserList.appendChild(userElement);
            });
        }
    }

    function renderChannels() {
        channelsList.innerHTML = '';
        channels.forEach(channel => {
            const li = document.createElement('li');
            li.textContent = channel.name;
            li.className = channel.type;
            li.dataset.channel = channel.name;
            li.dataset.type = channel.type;
            
            if (channel.name === currentRoom) {
                li.classList.add('active');
            }
            
            li.onclick = () => switchChannel(channel.name, channel.type);
            channelsList.appendChild(li);
        });
    }

    function switchChannel(channelName, channelType) {
        if (currentRoom === channelName) return;
        
        // Leave voice if switching from voice channel
        if (currentRoomType === 'voice' && isInVoice) {
            leaveVoice();
        }
        
        currentRoom = channelName;
        currentRoomType = channelType;
        
        // Show/hide appropriate UI
        if (channelType === 'text') {
            chatArea.classList.remove('hidden');
            voiceArea.classList.add('hidden');
        } else {
            chatArea.classList.add('hidden');
            voiceArea.classList.remove('hidden');
        }
        
        renderChannels();
        connect();
    }

    async function joinVoice() {
        try {
            // Try to get audio, but don't require it
            let constraints = { audio: true, video: false };
            
            try {
                localStream = await navigator.mediaDevices.getUserMedia(constraints);
            } catch (audioError) {
                console.log('Audio not available, joining voice without microphone');
                // Create a silent audio track
                localStream = new MediaStream();
            }
            
            isInVoice = true;
            joinVoiceBtn.classList.add('hidden');
            leaveVoiceBtn.classList.remove('hidden');
            
            sendMessage({
                type: 'join_voice'
            });
            
            addVideoElement(currentUsername, localStream, true);
            
        } catch (error) {
            console.error('Fehler beim Beitreten des Voice-Channels:', error);
            // Still allow joining without microphone
            isInVoice = true;
            joinVoiceBtn.classList.add('hidden');
            leaveVoiceBtn.classList.remove('hidden');
            
            sendMessage({
                type: 'join_voice'
            });
            
            addVideoElement(currentUsername, null, true);
        }
    }

    function leaveVoice() {
        if (localStream) {
            localStream.getTracks().forEach(track => track.stop());
            localStream = null;
        }
        
        // Close all peer connections
        peerConnections.forEach(pc => pc.close());
        peerConnections.clear();
        
        // Clear video grid
        videoGrid.innerHTML = '';
        
        isInVoice = false;
        isVideoEnabled = false;
        isScreenSharing = false;
        
        joinVoiceBtn.classList.remove('hidden');
        leaveVoiceBtn.classList.add('hidden');
        videoBtn.classList.remove('active');
        screenShareBtn.classList.remove('active');
        
        sendMessage({
            type: 'leave_voice'
        });
    }

    async function toggleVideo() {
        if (!isInVoice) return;
        
        try {
            if (!isVideoEnabled) {
                const videoStream = await navigator.mediaDevices.getUserMedia({ 
                    video: true, 
                    audio: true 
                });
                
                localStream = videoStream;
                isVideoEnabled = true;
                videoBtn.classList.add('active');
                
                updateVideoElement(currentUsername, localStream);
                
            } else {
                localStream.getVideoTracks().forEach(track => track.stop());
                try {
                    localStream = await navigator.mediaDevices.getUserMedia({ audio: true });
                } catch (e) {
                    localStream = new MediaStream();
                }
                
                isVideoEnabled = false;
                videoBtn.classList.remove('active');
                
                updateVideoElement(currentUsername, localStream);
            }
            
            updatePeerConnections();
            
            sendMessage({
                type: 'video_toggle',
                data: isVideoEnabled
            });
            
        } catch (error) {
            console.error('Fehler beim Video-Toggle:', error);
        }
    }

    async function toggleScreenShare() {
        if (!isInVoice) return;
        
        try {
            if (!isScreenSharing) {
                const screenStream = await navigator.mediaDevices.getDisplayMedia({ 
                    video: true, 
                    audio: true 
                });
                
                localStream = screenStream;
                isScreenSharing = true;
                screenShareBtn.classList.add('active');
                
                screenStream.getVideoTracks()[0].onended = () => {
                    toggleScreenShare();
                };
                
            } else {
                localStream.getTracks().forEach(track => track.stop());
                try {
                    localStream = await navigator.mediaDevices.getUserMedia({ 
                        audio: true, 
                        video: isVideoEnabled 
                    });
                } catch (e) {
                    localStream = new MediaStream();
                }
                
                isScreenSharing = false;
                screenShareBtn.classList.remove('active');
            }
            
            updateVideoElement(currentUsername, localStream);
            updatePeerConnections();
            
            sendMessage({
                type: 'screen_share',
                data: isScreenSharing
            });
            
        } catch (error) {
            console.error('Fehler beim Screen-Share:', error);
        }
    }

    function toggleMute() {
        if (!localStream) return;
        
        const audioTrack = localStream.getAudioTracks()[0];
        if (audioTrack) {
            audioTrack.enabled = !audioTrack.enabled;
            isMuted = !audioTrack.enabled;
            
            if (isMuted) {
                muteBtn.classList.add('muted');
                muteBtn.textContent = 'Stumm (An)';
            } else {
                muteBtn.classList.remove('muted');
                muteBtn.textContent = 'Stumm';
            }
            
            sendMessage({
                type: 'audio_toggle',
                data: isMuted
            });
        }
    }

    function addVideoElement(username, stream, isLocal = false) {
        // Remove existing element if it exists
        removeVideoElement(username);
        
        const container = document.createElement('div');
        container.className = 'video-container';
        container.id = `video-${username}`;
        
        if (stream && stream.getVideoTracks().length > 0) {
            const video = document.createElement('video');
            video.srcObject = stream;
            video.autoplay = true;
            video.muted = isLocal;
            container.appendChild(video);
        } else {
            // Audio-only user
            const audioIcon = document.createElement('div');
            audioIcon.className = 'audio-only';
            audioIcon.textContent = '🎤';
            container.appendChild(audioIcon);
        }
        
        const label = document.createElement('div');
        label.className = 'username';
        label.textContent = username;
        
        container.appendChild(label);
        videoGrid.appendChild(container);
    }

    function updateVideoElement(username, stream) {
        const container = document.getElementById(`video-${username}`);
        if (container) {
            // Clear container
            container.innerHTML = '';
            
            if (stream && stream.getVideoTracks().length > 0) {
                const video = document.createElement('video');
                video.srcObject = stream;
                video.autoplay = true;
                video.muted = username === currentUsername;
                container.appendChild(video);
            } else {
                const audioIcon = document.createElement('div');
                audioIcon.className = 'audio-only';
                audioIcon.textContent = '🎤';
                container.appendChild(audioIcon);
            }
            
            const label = document.createElement('div');
            label.className = 'username';
            label.textContent = username;
            container.appendChild(label);
        }
    }

    function removeVideoElement(username) {
        const container = document.getElementById(`video-${username}`);
        if (container) {
            container.remove();
        }
    }

    async function handleWebRTCInitiate(data) {
        if (isInVoice) {
            const pc = await createPeerConnection(data.from);
            const offer = await pc.createOffer();
            await pc.setLocalDescription(offer);
            
            sendMessage({
                type: 'webrtc_offer',
                target: data.from,
                data: offer
            });
        }
    }

    async function createPeerConnection(targetUserId) {
        const pc = new RTCPeerConnection(rtcConfiguration);
        peerConnections.set(targetUserId, pc);
        
        if (localStream) {
            localStream.getTracks().forEach(track => {
                pc.addTrack(track, localStream);
            });
        }
        
        pc.ontrack = (event) => {
            const remoteStream = event.streams[0];
            // Find username by user ID (simplified - in real app you'd track this)
            addVideoElement(`User-${targetUserId.slice(-4)}`, remoteStream);
        };
        
        pc.onicecandidate = (event) => {
            if (event.candidate) {
                sendMessage({
                    type: 'webrtc_ice',
                    target: targetUserId,
                    data: event.candidate
                });
            }
        };
        
        return pc;
    }

    async function handleWebRTCOffer(data) {
        const pc = await createPeerConnection(data.from);
        await pc.setRemoteDescription(data.data);
        
        const answer = await pc.createAnswer();
        await pc.setLocalDescription(answer);
        
        sendMessage({
            type: 'webrtc_answer',
            target: data.from,
            data: answer
        });
    }

    async function handleWebRTCAnswer(data) {
        const pc = peerConnections.get(data.from);
        if (pc) {
            await pc.setRemoteDescription(data.data);
        }
    }

    async function handleWebRTCICE(data) {
        const pc = peerConnections.get(data.from);
        if (pc) {
            await pc.addIceCandidate(data.data);
        }
    }

    function updatePeerConnections() {
        peerConnections.forEach(async (pc, userId) => {
            const senders = pc.getSenders();
            senders.forEach(sender => {
                if (sender.track) {
                    pc.removeTrack(sender);
                }
            });
            
            if (localStream) {
                localStream.getTracks().forEach(track => {
                    pc.addTrack(track, localStream);
                });
            }
        });
    }

    function sendMessage(message) {
        if (ws && ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify(message));
        } else {
            console.error('WebSocket not connected');
        }
    }

    // Event Listeners
    joinButton.onclick = () => {
        currentUsername = usernameInput.value.trim();
        currentGuild = guildNameInput.value.trim();
        currentRoom = channels[0].name;
        currentRoomType = channels[0].type;

        if (!currentUsername || !currentGuild) {
            alert('Benutzername und Gildenname sind erforderlich.');
            return;
        }

        console.log('Joining with:', { currentUsername, currentGuild, currentRoom, currentRoomType });

        joinScreen.classList.add('hidden');
        appContainer.classList.remove('hidden');
        
        // Show text chat by default
        chatArea.classList.remove('hidden');
        voiceArea.classList.add('hidden');
        
        renderChannels();
        connect();
    };

    messageInput.onkeydown = (event) => {
        if (event.key === 'Enter' && messageInput.value.trim() !== '') {
            sendMessage({
                type: 'new_message',
                content: messageInput.value
            });
            messageInput.value = '';
        }
    };

    joinVoiceBtn.onclick = joinVoice;
    leaveVoiceBtn.onclick = leaveVoice;
    muteBtn.onclick = toggleMute;
    videoBtn.onclick = toggleVideo;
    screenShareBtn.onclick = toggleScreenShare;

</script>

</body>
</html>
